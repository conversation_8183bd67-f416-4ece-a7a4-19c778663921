from __future__ import annotations
# ------------------------- stdlib -------------------------
import os, json, math, tempfile, time, base64, io, logging, traceback, sys
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
# ------------------------- third-party -------------------------
import numpy as np
import pandas as pd
import mne
from mne.preprocessing import ICA
import matplotlib
matplotlib.use("Agg")  # headless safe
import matplotlib.pyplot as plt
import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots
from taipy.gui import Gui, State, notify, Markdown
from fpdf import FPDF
import qrcode
# Try optional kaleido for Plotly static images
try:
    import kaleido  # noqa: F401
    _HAS_KALEIDO = True
except Exception:
    _HAS_KALEIDO = False
# ---------------- CONFIG ----------------
if os.path.exists("logo.svg"):
    with open("logo.svg", "rb") as f:
        LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(f.read()).decode()
else:
    svg = """<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="40" stroke="#22c55e" stroke-width="4" fill="#fde047" />
        <text x="50" y="56" font-size="24" text-anchor="middle" fill="#111827">EEG</text>
    </svg>"""
    LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(svg.encode()).decode()
# ---------------- Enhanced Logging with in-app buffer ----------------
class DebugHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.logs: List[Dict[str, Any]] = []
        self.max_logs = 2000
        
    def emit(self, record):
        try:
            log_entry = {
                'timestamp': datetime.fromtimestamp(record.created).strftime('%H:%M:%S.%f')[:-3],
                'level': record.levelname,
                'message': self.format(record),
                'module': getattr(record, 'module', 'unknown'),
                'funcName': getattr(record, 'funcName', 'unknown'),
                'lineno': getattr(record, 'lineno', 0),
                'pathname': getattr(record, 'pathname', ''),
            }
            self.logs.append(log_entry)
            if len(self.logs) > self.max_logs:
                self.logs = self.logs[-self.max_logs:]
        except Exception as e:
            # Fallback logging to prevent infinite recursion
            print(f"Debug handler error: {e}", file=sys.stderr)
            
    def get_logs(self, level: Optional[str] = None, limit: int = 200):
        logs = self.logs[-limit:] if limit else self.logs
        if level:
            return [log for log in logs if log['level'] == level]
        return list(logs)
        
    def clear_logs(self):
        self.logs.clear()
# Initialize debug handler
debug_handler = DebugHandler()
debug_handler.setFormatter(logging.Formatter('%(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s'))
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[logging.StreamHandler(), debug_handler]
)
logger = logging.getLogger("EEGApp")
# ---------------- Enhanced Perf decorator with error handling ----------------
def monitor_performance(func):
    def wrapper(*args, **kwargs):
        # Handle both regular functions and state-bound methods
        func_name = getattr(func, '__name__', str(func))
        t0 = time.time()
        try:
            result = func(*args, **kwargs)
            dt = time.time() - t0
            logger.debug(f"{func_name} completed in {dt:.3f}s")
            return result
        except Exception as e:
            dt = time.time() - t0
            error_msg = f"{func_name} failed after {dt:.3f}s: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Full traceback for {func_name}:\n{traceback.format_exc()}")
            # Re-raise with additional context
            raise Exception(f"{error_msg}") from e
    return wrapper
# ---------------- Utilities ----------------
_DEF_LINE_FREQ = 50.0  # Hz for EU
@monitor_performance
def load_raw(path: str) -> mne.io.BaseRaw:
    ext = os.path.splitext(path)[1].lower()
    loaders = {
        ".edf": mne.io.read_raw_edf,
        ".bdf": mne.io.read_raw_bdf if hasattr(mne.io, "read_raw_bdf") else mne.io.read_raw_edf,
        ".fif": mne.io.read_raw_fif,
        ".set": mne.io.read_raw_eeglab,
    }
    if ext not in loaders:
        raise ValueError(f"Unsupported file type: {ext}. Supported: {list(loaders.keys())}")
    
    logger.info(f"Loading file: {path} (type: {ext})")
    
    # Check if file exists and is readable
    if not os.path.exists(path):
        raise FileNotFoundError(f"File not found: {path}")
    if not os.access(path, os.R_OK):
        raise PermissionError(f"Cannot read file: {path}")
    
    raw = loaders[ext](path, preload=True)
    logger.info(f"Successfully loaded: {raw.info['nchan']} channels, {raw.n_times} samples, "
               f"{raw.info['sfreq']} Hz, {raw.times[-1]:.2f}s duration")
    return raw
@monitor_performance
def preprocess(raw: mne.io.BaseRaw, ref: str, l: float, h: float) -> mne.io.BaseRaw:
    raw = raw.copy()
    logger.info(f"Preprocessing: reference={ref}, band={l:.2f}-{h:.2f} Hz")
    
    # Validate parameters
    if h <= l:
        raise ValueError(f"High frequency ({h}) must be greater than low frequency ({l})")
    if h > raw.info['sfreq'] / 2:
        logger.warning(f"High frequency ({h}) exceeds Nyquist frequency ({raw.info['sfreq']/2})")
        h = raw.info['sfreq'] / 2 - 1
    
    # Referencing
    if ref == "average":
        logger.debug("Applying average reference")
        raw.set_eeg_reference("average", projection=False)
    elif ref == "mastoid":
        mastoid_channels = [ch for ch in ("M1", "M2", "TP9", "TP10") if ch in raw.ch_names]
        if mastoid_channels:
            logger.debug(f"Applying mastoid reference using channels: {mastoid_channels}")
            raw.set_eeg_reference(mastoid_channels, projection=False)
        else:
            logger.warning("No mastoid channels found; falling back to average reference")
            raw.set_eeg_reference("average", projection=False)
    else:
        logger.warning(f"Unknown reference '{ref}'; using average")
        raw.set_eeg_reference("average", projection=False)
    
    # Filtering (zero-phase FIR)
    logger.debug(f"Applying bandpass filter: {l}-{h} Hz")
    raw.filter(l, h, method='fir', phase='zero-double', picks='eeg', verbose=False)
    return raw
@monitor_performance
def run_ica(raw: mne.io.BaseRaw, n_components: int = 20) -> Tuple[mne.io.BaseRaw, ICA]:
    logger.info(f"Running ICA decomposition (n_components={n_components})")
    
    # Validate n_components
    max_components = min(raw.info['nchan'], raw.n_times // 2)
    if n_components > max_components:
        n_components = max_components
        logger.warning(f"Reducing n_components to {n_components} (max possible)")
    
    ica = ICA(n_components=n_components, random_state=97, max_iter='auto')
    
    # High-pass filter for ICA (recommended >= 1 Hz)
    logger.debug("Applying high-pass filter for ICA (1 Hz)")
    raw_for_ica = raw.copy().filter(1.0, None, verbose=False)
    
    ica.fit(raw_for_ica)
    logger.info(f"ICA fitted with {ica.n_components_} components")
    
    cleaned = ica.apply(raw.copy())
    logger.info("ICA applied to raw data")
    
    return cleaned, ica
# ---------------- Enhanced Metrics (QA) ----------------
@monitor_performance
def _bandpower(psd: np.ndarray, freqs: np.ndarray, fmin: float, fmax: float) -> np.ndarray:
    """Calculate mean power in frequency band"""
    if len(psd.shape) != 2 or len(freqs.shape) != 1:
        raise ValueError(f"Invalid shapes: psd={psd.shape}, freqs={freqs.shape}")
    
    mask = (freqs >= fmin) & (freqs < fmax)
    if not np.any(mask):
        logger.warning(f"No frequencies found in band {fmin}-{fmax} Hz")
        return np.zeros(psd.shape[0])
    
    return psd[:, mask].mean(axis=1)
@monitor_performance
def compute_metrics(raw: mne.io.BaseRaw, strict: str) -> Dict[str, Any]:
    """Compute comprehensive EEG quality metrics"""
    logger.info(f"Computing quality metrics (strictness: {strict})")
    
    # Get EEG data
    eeg_picks = mne.pick_types(raw.info, eeg=True, exclude='bads')
    if len(eeg_picks) == 0:
        raise ValueError("No EEG channels found")
    
    data_v = raw.get_data(picks=eeg_picks)  # in Volts
    data_uv = data_v * 1e6  # convert to µV
    sf = float(raw.info['sfreq'])
    
    logger.debug(f"Data shape: {data_uv.shape}, sampling rate: {sf} Hz")
    # PSD computation with error handling
    try:
        fmax = min(90.0, sf/2.0 - 1)  # Avoid Nyquist issues
        psds, freqs = mne.time_frequency.psd_welch(
            raw, picks=eeg_picks, fmin=0.5, fmax=fmax, verbose=False
        )
        psds_uv = psds * (1e6 ** 2)  # Convert to µV²/Hz
        logger.debug(f"PSD computed: {psds_uv.shape}, freq range: {freqs.min():.1f}-{freqs.max():.1f} Hz")
    except Exception as e:
        logger.error(f"PSD computation failed: {e}")
        # Fallback metrics without PSD
        return _compute_basic_metrics(raw, data_uv, strict)
    # Band power calculations
    try:
        p_1_40 = _bandpower(psds_uv, freqs, 1.0, min(40.0, fmax))
        p_line = _bandpower(psds_uv, freqs, _DEF_LINE_FREQ-1.0, _DEF_LINE_FREQ+1.0)
        p_hf = _bandpower(psds_uv, freqs, 60.0, fmax) if fmax >= 60 else np.zeros_like(p_1_40)
        
        # SNR calculation
        noise_power = p_line + p_hf + 1e-12  # Add small epsilon
        snr_chan = p_1_40 / noise_power
        snr_median = float(np.median(snr_chan[np.isfinite(snr_chan)]))
        
        logger.debug(f"SNR computed: median={snr_median:.2f}")
    except Exception as e:
        logger.error(f"Band power calculation failed: {e}")
        snr_median = 0.0
        p_line = np.zeros(data_uv.shape[0])
    # Amplitude-based metrics
    ptp_uv = data_uv.ptp(axis=1)
    n_flat = int(np.sum(ptp_uv < 5.0))  # Channels with <5 µV range
    
    # Kurtosis (measure of signal spikiness)
    try:
        data_centered = data_uv - data_uv.mean(axis=1, keepdims=True)
        var = data_centered.var(axis=1, ddof=1) + 1e-12
        m4 = (data_centered**4).mean(axis=1)
        kurt_excess = m4 / (var**2) - 3.0
        n_high_kurt = int(np.sum(kurt_excess > 10.0))
        logger.debug(f"Kurtosis computed: {n_high_kurt} channels with high kurtosis")
    except Exception as e:
        logger.warning(f"Kurtosis calculation failed: {e}")
        n_high_kurt = 0
    # Quality thresholds
    thresholds = {
        "low":    {"snr": 2.0, "flat": 5, "kurt": 25, "noise": 50.0},
        "medium": {"snr": 5.0, "flat": 3, "kurt": 15, "noise": 25.0},
        "high":   {"snr": 10.0, "flat": 1, "kurt": 10, "noise": 15.0},
    }
    
    if strict not in thresholds:
        logger.warning(f"Unknown strictness '{strict}', using 'medium'")
        strict = "medium"
    
    thr = thresholds[strict]
    # Build metrics dictionary
    metrics = {
        "Median SNR (1–40Hz / noise)": round(snr_median, 2),
        "Flat Channels (<5 µV ptp)": n_flat,
        "High Kurtosis Channels (>10)": n_high_kurt,
        f"Line Noise @{int(_DEF_LINE_FREQ)}Hz (µV²/Hz)": round(float(np.median(p_line)), 2),
        "Pass SNR": snr_median >= thr["snr"],
        "Pass Flat": n_flat <= thr["flat"],
        "Pass Kurtosis": n_high_kurt <= thr["kurt"],
        "Pass Line Noise": float(np.median(p_line)) <= thr["noise"],
        "Sampling Rate (Hz)": f"{sf:.2f}",
        "Duration (s)": f"{raw.times[-1]:.2f}",
        "N Channels": len(eeg_picks),
        "Data Range (µV)": f"[{data_uv.min():.1f}, {data_uv.max():.1f}]",
        "Data Std (µV)": f"{data_uv.std():.2f}",
        "Max Amplitude (µV)": f"{np.abs(data_uv).max():.1f}",
        "Zero Crossings/s": f"{np.mean([np.sum(np.diff(np.sign(ch)) != 0) for ch in data_uv]) / raw.times[-1]:.1f}",
    }
    
    # Overall quality assessment
    quality_checks = [
        metrics["Pass SNR"], 
        metrics["Pass Flat"], 
        metrics["Pass Kurtosis"], 
        metrics["Pass Line Noise"]
    ]
    n_passed = sum(quality_checks)
    
    if n_passed == 4:
        quality = "Excellent"
    elif n_passed == 3:
        quality = "Good"
    elif n_passed == 2:
        quality = "Fair"
    elif n_passed == 1:
        quality = "Poor"
    else:
        quality = "Very Poor"
    
    metrics["Overall Quality"] = quality
    metrics["Quality Score"] = f"{n_passed}/4"
    
    logger.info(f"Quality assessment complete: {quality} ({n_passed}/4 checks passed)")
    
    # Convert to DataFrame for table display
    metrics_list = [{"Metric": k, "Value": v} for k, v in metrics.items()]
    return pd.DataFrame(metrics_list)
def _compute_basic_metrics(raw: mne.io.BaseRaw, data_uv: np.ndarray, strict: str) -> Dict[str, Any]:
    """Fallback metrics when PSD computation fails"""
    logger.warning("Computing basic metrics only (PSD unavailable)")
    
    ptp_uv = data_uv.ptp(axis=1)
    n_flat = int(np.sum(ptp_uv < 5.0))
    
    basic_metrics = {
        "Flat Channels (<5 µV ptp)": n_flat,
        "Sampling Rate (Hz)": f"{raw.info['sfreq']:.2f}",
        "Duration (s)": f"{raw.times[-1]:.2f}",
        "N Channels": data_uv.shape[0],
        "Data Range (µV)": f"[{data_uv.min():.1f}, {data_uv.max():.1f}]",
        "Data Std (µV)": f"{data_uv.std():.2f}",
        "Overall Quality": "Unknown (PSD failed)",
        "Quality Score": "?/4"
    }
    
    # Convert to DataFrame for table display
    metrics_list = [{"Metric": k, "Value": v} for k, v in basic_metrics.items()]
    return pd.DataFrame(metrics_list)
# ---------------- QR helper ----------------
def qr_code(data: str) -> str:
    try:
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buf = io.BytesIO()
        img.save(buf, format="PNG")
        return "data:image/png;base64," + base64.b64encode(buf.getvalue()).decode()
    except Exception as e:
        logger.error(f"QR generation failed: {e}")
        return ""
# ---------------- Enhanced Plotting ----------------
@monitor_performance
def plot_raw(raw: mne.io.BaseRaw, picks: List[str], start: float = 0.0, window: float = 10.0, decim: int = 5):
    """Plot raw EEG traces"""
    if not picks:
        picks = raw.ch_names[:min(10, len(raw.ch_names))]
    
    # Ensure picks exist in raw
    valid_picks = [p for p in picks if p in raw.ch_names]
    if not valid_picks:
        logger.warning("No valid channels selected, using first 10")
        valid_picks = raw.ch_names[:min(10, len(raw.ch_names))]
    
    sf = float(raw.info['sfreq'])
    start_samp = max(0, int(start * sf))
    stop_samp = min(raw.n_times, int((start + window) * sf))
    
    if start_samp >= stop_samp:
        raise ValueError(f"Invalid time window: {start}-{start+window} s")
    
    try:
        data_v = raw.get_data(picks=valid_picks, start=start_samp, stop=stop_samp)
        data_uv = data_v * 1e6
        times = raw.times[start_samp:stop_samp]
        
        # Decimation for performance
        if len(times) > 10000:  # Only decimate if needed
            data_uv = data_uv[:, ::decim]
            times = times[::decim]
        
        logger.debug(f"Plotting {len(valid_picks)} channels, {len(times)} time points")
        
        fig = make_subplots(
            rows=len(valid_picks), cols=1, 
            shared_xaxes=True, 
            vertical_spacing=0.02,
            subplot_titles=valid_picks
        )
        
        colors = px.colors.qualitative.Dark24
        for i, (ch, ch_data) in enumerate(zip(valid_picks, data_uv)):
            color = colors[i % len(colors)]
            fig.add_trace(
                go.Scatter(
                    x=times,
                    y=ch_data,
                    mode="lines",
                    line=dict(width=1.5, color=color),
                    name=ch,
                    hovertemplate=f"{ch}<br>Time: %{{x:.2f}} s<br>Amplitude: %{{y:.1f}} µV<extra></extra>",
                    showlegend=False,
                ),
                row=i+1, col=1
            )
            
            # Add y-axis labels
            fig.update_yaxes(title_text="µV", row=i+1, col=1, title_standoff=0)
        fig.update_layout(
            title=dict(
                text=f"EEG Raw Trace ({start:.1f}–{start+window:.1f} s, {len(valid_picks)} channels)",
                x=0.5
            ),
            hovermode="closest",
            height=max(400, 100 + 80 * len(valid_picks)),
            template="plotly_dark",
            margin=dict(l=60, r=20, t=50, b=50),
            showlegend=False
        )
        
        # Update x-axis for bottom subplot only
        fig.update_xaxes(title_text="Time (s)", row=len(valid_picks), col=1)
        
        return fig
    except Exception as e:
        logger.error(f"Raw plot failed: {e}")
        raise
@monitor_performance
def plot_psd(raw: mne.io.BaseRaw):
    """Plot power spectral density"""
    try:
        eeg_picks = mne.pick_types(raw.info, eeg=True, exclude='bads')
        if len(eeg_picks) == 0:
            raise ValueError("No EEG channels found")
        
        fmax = min(60.0, raw.info['sfreq']/2.0 - 1)
        psds, freqs = mne.time_frequency.psd_welch(
            raw, picks=eeg_picks[:min(15, len(eeg_picks))], 
            fmin=0.5, fmax=fmax, verbose=False
        )
        psds_uv = psds * (1e6 ** 2)  # Convert to µV²/Hz
        
        fig = go.Figure()
        colors = px.colors.qualitative.Set3
        
        # Plot individual channels
        channel_names = [raw.ch_names[i] for i in eeg_picks[:min(15, len(eeg_picks))]]
        for i, (ch, psd) in enumerate(zip(channel_names, psds_uv)):
            fig.add_trace(go.Scatter(
                x=freqs, 
                y=psd, 
                name=ch, 
                opacity=0.7,
                line=dict(color=colors[i % len(colors)]),
                hovertemplate=f"{ch}<br>Freq: %{{x:.1f}} Hz<br>Power: %{{y:.2e}} µV²/Hz<extra></extra>",
            ))
        
        # Add median line
        median_psd = np.median(psds_uv, axis=0)
        fig.add_trace(go.Scatter(
            x=freqs, 
            y=median_psd, 
            name="Median", 
            line=dict(width=3, color="white", dash="dash"),
            hovertemplate="Median<br>Freq: %{x:.1f} Hz<br>Power: %{y:.2e} µV²/Hz<extra></extra>",
        ))
        
        fig.update_layout(
            title="Power Spectral Density (Welch Method)",
            xaxis_title="Frequency (Hz)", 
            yaxis_title="Power (µV²/Hz)",
            yaxis_type="log",
            template="plotly_dark", 
            height=450,
            legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02),
            margin=dict(r=100)
        )
        
        return fig
    except Exception as e:
        logger.error(f"PSD plot failed: {e}")
        raise
@monitor_performance
def plot_topo(raw: mne.io.BaseRaw) -> Optional[str]:
    """Plot electrode topography"""
    try:
        eeg_picks = mne.pick_types(raw.info, eeg=True, exclude='bads')
        if len(eeg_picks) == 0:
            logger.warning("No EEG channels for topography")
            return None
        
        fig, ax = plt.subplots(figsize=(8, 6), facecolor='black')
        ax.set_facecolor('black')
        
        # Create a subset info for plotting
        info_subset = mne.pick_info(raw.info, eeg_picks)
        mne.viz.plot_sensors(info_subset, kind='topomap', show=False, axes=ax)
        
        ax.set_title('Electrode Locations', color='white', fontsize=14)
        
        buf = io.BytesIO()
        fig.savefig(buf, format="png", dpi=150, facecolor='black', 
                   bbox_inches='tight', edgecolor='none')
        plt.close(fig)
        
        return f"data:image/png;base64,{base64.b64encode(buf.getvalue()).decode()}"
    except Exception as e:
        logger.error(f"Topography plot failed: {e}")
        return None
@monitor_performance
def plot_bandpower(raw: mne.io.BaseRaw):
    """Plot power in different frequency bands"""
    try:
        eeg_picks = mne.pick_types(raw.info, eeg=True, exclude='bads')
        if len(eeg_picks) == 0:
            raise ValueError("No EEG channels found")
        
        fmax = min(40.0, raw.info['sfreq']/2.0 - 1)
        psds, freqs = mne.time_frequency.psd_welch(
            raw, picks=eeg_picks, fmin=0.5, fmax=fmax, verbose=False
        )
        psds_uv = psds * (1e6 ** 2)
        
        bands = {
            "Delta (0.5-4 Hz)": (0.5, 4),
            "Theta (4-8 Hz)": (4, 8),
            "Alpha (8-13 Hz)": (8, 13),
            "Beta (13-30 Hz)": (13, 30),
            "Gamma (30-40 Hz)": (30, min(40, fmax)),
        }
        
        # Calculate band powers
        channel_names = [raw.ch_names[i] for i in eeg_picks]
        band_data = []
        
        for band_name, (fmin, fmax_band) in bands.items():
            if fmax_band <= fmin:  # Skip if band is invalid
                continue
            mask = (freqs >= fmin) & (freqs < fmax_band)
            if not np.any(mask):
                continue
            power = psds_uv[:, mask].mean(axis=1)
            
            for ch_idx, ch_name in enumerate(channel_names):
                band_data.append({
                    'Channel': ch_name,
                    'Band': band_name,
                    'Power': power[ch_idx]
                })
        
        # Create grouped bar chart
        fig = go.Figure()
        bands_list = list(bands.keys())
        colors = px.colors.qualitative.Set2
        
        for i, band in enumerate(bands_list):
            band_powers = [d['Power'] for d in band_data if d['Band'] == band]
            if band_powers:  # Only add if we have data
                fig.add_trace(go.Bar(
                    name=band,
                    x=channel_names[:len(band_powers)],
                    y=band_powers,
                    opacity=0.8,
                    marker_color=colors[i % len(colors)],
                    hovertemplate=f"{band}<br>Channel: %{{x}}<br>Power: %{{y:.2f}} µV²/Hz<extra></extra>"
                ))
        
        fig.update_layout(
            title="Band Power Analysis",
            barmode='group',
            template="plotly_dark",
            height=480,
            xaxis_title="Channel",
            yaxis_title="Power (µV²/Hz)",
            yaxis_type="log",
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="center", x=0.5)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Band power plot failed: {e}")
        raise
@monitor_performance
def plot_ica(ica: ICA, raw: mne.io.BaseRaw) -> Optional[str]:
    """Plot ICA components"""
    try:
        # Create components plot
        figs = ica.plot_components(picks=range(min(20, ica.n_components_)), show=False)
        
        # Handle both single figure and list of figures
        if isinstance(figs, list):
            fig = figs[0]
        else:
            fig = figs
            
        # Customize appearance
        fig.patch.set_facecolor('black')
        for ax in fig.axes:
            ax.set_facecolor('black')
            ax.tick_params(colors='white')
            for spine in ax.spines.values():
                spine.set_edgecolor('white')
        
        fig.suptitle(f'ICA Components (showing {min(20, ica.n_components_)} of {ica.n_components_})', 
                    color='white', fontsize=14)
        
        buf = io.BytesIO()
        fig.savefig(buf, format="png", dpi=120, facecolor='black', 
                   bbox_inches='tight', edgecolor='none')
        plt.close(fig)
        
        return f"data:image/png;base64,{base64.b64encode(buf.getvalue()).decode()}"
    except Exception as e:
        logger.error(f"ICA plot failed: {e}")
        return None
@monitor_performance
def plot_events(raw: mne.io.BaseRaw):
    """Plot event timeline"""
    try:
        anns = raw.annotations
        if anns is None or len(anns) == 0:
            logger.info("No events/annotations found")
            return None
        
        times = np.array(anns.onset)
        durs = np.array(anns.duration)
        descriptions = list(anns.description)
        
        # Create unique colors for different event types
        unique_descriptions = list(set(descriptions))
        colors = px.colors.qualitative.Set1
        color_map = {desc: colors[i % len(colors)] for i, desc in enumerate(unique_descriptions)}
        
        fig = go.Figure()
        
        for i, (t, d, desc) in enumerate(zip(times, durs, descriptions)):
            fig.add_trace(go.Scatter(
                x=[t, t + d] if d > 0 else [t, t + 0.1],
                y=[i, i],
                mode="lines+markers",
                line=dict(width=8, color=color_map[desc]),
                marker=dict(size=8, color=color_map[desc]),
                name=desc,
                legendgroup=desc,
                showlegend=desc not in [trace.name for trace in fig.data],
                hovertemplate=f"Event: {desc}<br>Start: %{{x:.2f}} s<br>Duration: {d:.2f} s<extra></extra>",
            ))
        
        fig.update_layout(
            title=f"Event Timeline ({len(anns)} events)",
            xaxis_title="Time (s)",
            yaxis_title="Event Index",
            template="plotly_dark",
            height=max(320, 50 + 20 * len(anns)),
            showlegend=True,
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="center", x=0.5)
        )
        
        return fig
    except Exception as e:
        logger.error(f"Events plot failed: {e}")
        return None
# ---------------- PDF REPORT ----------------
class PDF(FPDF):
    def header(self):
        if LOGO_B64:
            try:
                img_data = LOGO_B64.split(',')[1] if ',' in LOGO_B64 else LOGO_B64
                img_bytes = base64.b64decode(img_data)
                tmp = os.path.join(tempfile.gettempdir(), "_eeg_logo.png")
                with open(tmp, "wb") as f:
                    f.write(img_bytes)
                try:
                    self.image(tmp, 10, 8, 20)
                finally:
                    try: 
                        os.remove(tmp)
                    except Exception: 
                        pass
            except Exception as e:
                logger.debug(f"Logo insertion failed: {e}")
        
        self.set_font("Helvetica", "B", 16)
        self.cell(0, 10, "EEG Quality Assessment Report", 0, 1, "C")
        self.set_font("Helvetica", "I", 10)
        self.cell(0, 8, f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 0, 1, "C")
        self.ln(5)
    
    def footer(self):
        self.set_y(-15)
        self.set_font("Helvetica", "I", 8)
        self.cell(0, 10, f"Page {self.page_no()}", 0, 0, "C")
    
    def add_plot_image(self, title: str, img_path_or_b64: str):
        self.add_page()
        self.set_font("Helvetica", "B", 14)
        self.cell(0, 10, title, ln=True)
        self.ln(5)
        
        try:
            if img_path_or_b64.startswith("data:image/"):
                img_bytes = base64.b64decode(img_path_or_b64.split(',')[1])
                tmp = os.path.join(tempfile.gettempdir(), f"_plot_{abs(hash(title))}.png")
                with open(tmp, 'wb') as f: 
                    f.write(img_bytes)
                self.image(tmp, x=10, y=30, w=190)
                try: 
                    os.remove(tmp)
                except Exception: 
                    pass
            else:
                self.image(img_path_or_b64, x=10, y=30, w=190)
        except Exception as e:
            logger.error(f"PDF plot insertion failed ({title}): {e}")
            self.set_font("Helvetica", "I", 10)
            self.cell(0, 10, f"[Plot could not be generated: {str(e)}]", ln=True)
@monitor_performance
def export_report(raw: mne.io.BaseRaw, metrics: pd.DataFrame, dest: str) -> str:
    """Generate comprehensive PDF report"""
    logger.info(f"Generating PDF report: {dest}")
    
    pdf = PDF()
    pdf.add_page()
    
    # Quality metrics section
    pdf.set_font("Helvetica", "B", 14)
    pdf.cell(0, 10, "Quality Assessment", ln=True, align="C")
    pdf.ln(5)
    
    pdf.set_font("Helvetica", "", 11)
    for _, row in metrics.iterrows():
        key = row['Metric']
        value = row['Value']
        if isinstance(value, bool):
            value_str = "✓ PASS" if value else "✗ FAIL"
            pdf.set_font("Helvetica", "B" if value else "", 11)
        else:
            value_str = str(value)
            pdf.set_font("Helvetica", "", 11)
        
        pdf.cell(0, 8, f"{key}: {value_str}", ln=True)
    
    # Generate plots
    logger.debug("Generating plots for report")
    plot_data = []
    
    try:
        # Raw EEG trace
        picks = raw.ch_names[:min(8, len(raw.ch_names))]  # Limit for PDF
        raw_fig = plot_raw(raw, picks, window=5.0)  # Shorter window for PDF
        plot_data.append(("Raw EEG Trace", raw_fig))
    except Exception as e:
        logger.error(f"Raw plot for PDF failed: {e}")
    
    try:
        # PSD
        psd_fig = plot_psd(raw)
        plot_data.append(("Power Spectral Density", psd_fig))
    except Exception as e:
        logger.error(f"PSD plot for PDF failed: {e}")
    
    try:
        # Band power
        bp_fig = plot_bandpower(raw)
        plot_data.append(("Band Power Analysis", bp_fig))
    except Exception as e:
        logger.error(f"Band power plot for PDF failed: {e}")
    
    try:
        # Topography
        topo_img = plot_topo(raw)
        if topo_img:
            plot_data.append(("Electrode Topography", topo_img))
    except Exception as e:
        logger.error(f"Topography plot for PDF failed: {e}")
    
    # Save Plotly figures to images
    def _save_plotly_figure(fig, name: str) -> Optional[str]:
        if fig is None:
            return None
        
        out_path = os.path.join(tempfile.gettempdir(), f"{name.replace(' ', '_')}.png")
        try:
            if _HAS_KALEIDO:
                fig.write_image(out_path, format="png", width=1200, height=600)
                return out_path
            else:
                logger.warning(f"Kaleido not available; skipping {name} in PDF")
                return None
        except Exception as e:
            logger.error(f"Plotly figure export failed ({name}): {e}")
            return None
    
    # Add plots to PDF
    for title, fig_or_img in plot_data:
        if isinstance(fig_or_img, str):  # Base64 image
            pdf.add_plot_image(title, fig_or_img)
        else:  # Plotly figure
            img_path = _save_plotly_figure(fig_or_img, title)
            if img_path:
                pdf.add_plot_image(title, img_path)
    
    # Add QR code for file location
    pdf.add_page()
    pdf.set_font("Helvetica", "B", 12)
    pdf.cell(0, 10, "File Information", ln=True, align="C")
    pdf.ln(5)
    
    pdf.set_font("Helvetica", "", 10)
    pdf.cell(0, 8, f"Original file: {getattr(raw, 'filenames', ['Unknown'])[0] if hasattr(raw, 'filenames') else 'Unknown'}", ln=True)
    pdf.cell(0, 8, f"Report generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", ln=True)
    
    qr_img = qr_code(f"EEG Report - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    if qr_img:
        try:
            tmp = os.path.join(tempfile.gettempdir(), "_qr_report.png")
            with open(tmp, 'wb') as f: 
                f.write(base64.b64decode(qr_img.split(',')[1]))
            pdf.image(tmp, x=80, y=80, w=50)
            try: 
                os.remove(tmp)
            except Exception: 
                pass
        except Exception as e:
            logger.error(f"QR code insertion failed: {e}")
    
    # Save PDF
    pdf.output(dest)
    logger.info(f"PDF report saved successfully: {dest}")
    return dest
# ---------------- Module-Level State Variables (Taipy 3.0+ Compatible) ----------------
# Core data
file: str = ""
raw: Optional[mne.io.BaseRaw] = None
ica: Optional[ICA] = None

# Processing parameters
ref: str = "average"
band: List[float] = [1.0, 40.0]
strict: str = "medium"
picks: List[str] = []

# Results
metrics: pd.DataFrame = pd.DataFrame(columns=['Metric', 'Value'])
event_df: pd.DataFrame = pd.DataFrame(columns=['onset', 'duration', 'description'])

# UI state
loading: bool = False
status: str = "Ready - Upload an EEG file to begin"

# Debug state
debug: bool = False
show_debug_panel: bool = False
debug_logs: pd.DataFrame = pd.DataFrame(columns=['timestamp', 'level', 'module', 'funcName', 'message'])
debug_level: str = "INFO"
memory_usage: str = ""
performance_stats: pd.DataFrame = pd.DataFrame(columns=['Metric', 'Value'])

# Plot containers (will hold Plotly figures or base64 images)
plot_raw: Any = None
plot_psd: Any = None
plot_topo: str = ""
plot_bp: Any = None
plot_ica: str = ""
plot_events: Any = None
# ---------------- Enhanced Debug Helpers ----------------
def get_memory_usage() -> str:
    """Get current memory usage"""
    try:
        import psutil
        process = psutil.Process(os.getpid())
        mem_info = process.memory_info()
        return f"RSS: {mem_info.rss / 1024 / 1024:.1f} MB, VMS: {mem_info.vms / 1024 / 1024:.1f} MB"
    except ImportError:
        try:
            # Fallback using resource module
            import resource
            usage = resource.getrusage(resource.RUSAGE_SELF)
            return f"Peak: {usage.ru_maxrss / 1024:.1f} MB"  # Linux: KB, macOS: bytes
        except Exception:
            return "N/A"
    except Exception as e:
        return f"Error: {e}"
def get_system_info() -> Dict[str, str]:
    """Get system information for debugging"""
    import platform
    try:
        import psutil
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
    except ImportError:
        cpu_count = "N/A"
        cpu_percent = "N/A"
    
    return {
        "Platform": platform.platform(),
        "Python": platform.python_version(),
        "CPU Cores": str(cpu_count),
        "CPU Usage": f"{cpu_percent}%" if cpu_percent != "N/A" else "N/A",
        "MNE Version": mne.__version__,
    }
def update_debug_info(state, **kwargs):
    """Update debug information in state"""
    if not debug:
        return
    
    try:
        # Update memory usage
        global memory_usage, debug_logs
        memory_usage = get_memory_usage()
        
        # Get recent logs
        logs_data = debug_handler.get_logs(limit=200)
        debug_logs = pd.DataFrame(logs_data) if logs_data else pd.DataFrame(columns=['timestamp', 'level', 'module', 'funcName', 'message'])
        
        # Update performance stats
        global performance_stats
        perf_stats = [
            {"Metric": "Memory Usage", "Value": memory_usage},
            {"Metric": "Debug Logs", "Value": len(debug_handler.logs)},
            {"Metric": "Log Level", "Value": debug_level},
        ]
        
        if raw is not None:
            perf_stats.extend([
                {"Metric": "Channels", "Value": len(raw.ch_names)},
                {"Metric": "Samples", "Value": f"{raw.n_times:,}"},
                {"Metric": "Duration", "Value": f"{raw.times[-1]:.2f}s"},
                {"Metric": "Sampling Rate", "Value": f"{raw.info['sfreq']:.2f} Hz"},
                {"Metric": "File Size (MB)", "Value": f"{raw.get_data().nbytes / 1024 / 1024:.1f}"},
            ])
        
        if ica is not None:
            perf_stats.append({"Metric": "ICA Components", "Value": ica.n_components_})
        
        performance_stats = pd.DataFrame(perf_stats)
        
        logger.debug(f"Debug info updated: {len(debug_logs)} logs, {memory_usage}")
        
    except Exception as e:
        logger.error(f"Failed to update debug info: {e}")
# ---------------- Action Functions ----------------
@monitor_performance
def load_file_action(state, **kwargs):
    """Load EEG file"""
    global raw, ica, picks, metrics, event_df, loading, status, plot_raw, plot_psd, plot_topo, plot_bp, plot_ica, plot_events
    
    if not file:
        notify(state, "warning", "Please select a file first")
        return
    
    try:
        loading = True
        status = "Loading EEG file..."
        logger.info(f"Loading file: {file}")
        
        # Load the raw data
        raw = load_raw(file)
        
        # Initialize channel selection
        picks = list(raw.ch_names[:min(20, len(raw.ch_names))])  # Limit initial selection
        
        # Generate initial plots
        status = "Generating plots..."
        plot_all_data(state)
        
        status = f"Loaded: {len(raw.ch_names)} channels, {raw.times[-1]:.1f}s"
        
        update_debug_info(state)
        notify(state, "success", f"Successfully loaded {len(raw.ch_names)} channels")
        
    except Exception as e:
        error_msg = f"Failed to load file: {str(e)}"
        logger.error(error_msg)
        status = "Error loading file"
        notify(state, "error", error_msg)
    finally:
        loading = False
@monitor_performance
def delete_data_action(state, **kwargs):
    """Clear all data"""
    global file, raw, ica, picks, metrics, event_df, loading, status, plot_raw, plot_psd, plot_topo, plot_bp, plot_ica, plot_events, performance_stats
    
    logger.info("Clearing all data")
    
    # Reset all state variables
    file = ""
    raw = None
    ica = None
    picks = []
    metrics = pd.DataFrame(columns=['Metric', 'Value'])
    event_df = pd.DataFrame(columns=['onset', 'duration', 'description'])
    
    # Clear plots
    plot_raw = None
    plot_psd = None
    plot_topo = ""
    plot_bp = None
    plot_ica = ""
    plot_events = None
    
    performance_stats = pd.DataFrame(columns=['Metric', 'Value'])
    status = "Ready - Upload an EEG file to begin"
    
    update_debug_info(state)
    notify(state, "info", "All data cleared")
@monitor_performance
def preprocess_action(state, **kwargs):
    """Preprocess the EEG data"""
    global raw, loading, status, ica, plot_ica, metrics
    
    if not raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        loading = True
        status = "Preprocessing EEG data..."
        logger.info(f"Preprocessing with ref={ref}, band={band}")
        
        # Apply preprocessing
        raw = preprocess(raw, ref, band[0], band[1])
        
        # Clear dependent results
        ica = None
        plot_ica = ""
        metrics = pd.DataFrame(columns=['Metric', 'Value'])
        
        # Regenerate plots
        status = "Updating plots..."
        plot_all_data(state)
        
        status = f"Preprocessed: {ref} ref, {band[0]}-{band[1]} Hz"
        
        update_debug_info(state)
        notify(state, "success", "Preprocessing completed successfully")
        
    except Exception as e:
        error_msg = f"Preprocessing failed: {str(e)}"
        logger.error(error_msg)
        status = "Error in preprocessing"
        notify(state, "error", error_msg)
    finally:
        loading = False
@monitor_performance
def ica_action(state, **kwargs):
    """Run ICA decomposition"""
    global raw, ica, loading, status, plot_ica
    
    if not raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        loading = True
        status = "Running ICA decomposition..."
        logger.info("Starting ICA decomposition")
        
        # Run ICA
        n_components = min(20, len(raw.ch_names), raw.n_times // 10)
        raw, ica = run_ica(raw, n_components)
        
        # Generate ICA plot
        status = "Generating ICA plots..."
        plot_ica_result = plot_ica(ica, raw) or ""
        plot_ica = plot_ica_result
        
        # Regenerate other plots with cleaned data
        plot_all_data(state)
        
        status = f"ICA completed: {ica.n_components_} components"
        
        update_debug_info(state)
        notify(state, "success", f"ICA completed with {ica.n_components_} components")
        
    except Exception as e:
        error_msg = f"ICA failed: {str(e)}"
        logger.error(error_msg)
        status = "Error in ICA"
        notify(state, "error", error_msg)
    finally:
        loading = False
@monitor_performance
def qa_action(state, **kwargs):
    """Run quality assessment"""
    global raw, metrics, loading, status
    
    if not raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        loading = True
        status = "Computing quality metrics..."
        logger.info(f"Running QA with strictness: {strict}")
        
        # Compute quality metrics
        metrics = compute_metrics(raw, strict)
        
        # Extract quality info from DataFrame
        quality_row = metrics[metrics['Metric'] == 'Overall Quality']
        quality = quality_row['Value'].iloc[0] if not quality_row.empty else 'Unknown'
        
        score_row = metrics[metrics['Metric'] == 'Quality Score']
        score = score_row['Value'].iloc[0] if not score_row.empty else 'N/A'
        
        status = f"QA completed: {quality}"
        
        update_debug_info(state)
        
        # Notify based on quality
        if quality in ['Excellent', 'Good']:
            notify(state, "success", f"Data quality: {quality} ({score})")
        elif quality in ['Fair']:
            notify(state, "warning", f"Data quality: {quality} ({score})")
        else:
            notify(state, "error", f"Data quality: {quality} ({score})")
            
    except Exception as e:
        error_msg = f"Quality assessment failed: {str(e)}"
        logger.error(error_msg)
        status = "Error in QA"
        notify(state, "error", error_msg)
    finally:
        loading = False
@monitor_performance
def report_action(state, **kwargs):
    global raw, metrics, loading, status, strict
    """Generate PDF report"""
    if not raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        loading = True
        status = "Generating PDF report..."
        logger.info("Generating comprehensive PDF report")
        
        # Ensure we have metrics
        if metrics.empty:
            logger.info("Computing metrics for report")
            metrics = compute_metrics(raw, strict)
        
        # Generate report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        dest = os.path.join(tempfile.gettempdir(), f"EEG_Report_{timestamp}.pdf")
        
        export_report(raw, metrics, dest)
        
        status = f"Report generated: {os.path.basename(dest)}"
        
        update_debug_info(state)
        notify(state, "success", f"PDF report saved: {dest}")
        
    except Exception as e:
        error_msg = f"Report generation failed: {str(e)}"
        logger.error(error_msg)
        status = "Error generating report"
        notify(state, "error", error_msg)
    finally:
        loading = False
@monitor_performance
def plot_all_data(state, **kwargs):
    """Generate all plots for the current data"""
    global raw, picks, plot_raw, plot_psd, plot_topo, plot_bp, plot_events, event_df
    
    if not raw:
        return
    
    try:
        logger.debug("Generating all plots")
        
        # Raw trace plot
        picks_for_plot = picks[:15] if picks else raw.ch_names[:15]
        plot_raw_result = plot_raw(raw, picks_for_plot, window=10.0)
        plot_raw = plot_raw_result
        
        # PSD plot
        plot_psd_result = plot_psd(raw)
        plot_psd = plot_psd_result
        
        # Topography plot
        plot_topo_result = plot_topo(raw) or ""
        plot_topo = plot_topo_result
        
        # Band power plot
        plot_bp_result = plot_bandpower(raw)
        plot_bp = plot_bp_result
        
        # Events plot
        plot_events_result = plot_events(raw)
        plot_events = plot_events_result
        
        # Event table data
        anns = raw.annotations
        if anns is not None and len(anns) > 0:
            event_data = [
                {
                    "onset": f"{float(o):.2f}",
                    "duration": f"{float(d):.2f}",
                    "description": str(de)
                }
                for o, d, de in zip(anns.onset, anns.duration, anns.description)
            ]
            event_df = pd.DataFrame(event_data)
        else:
            event_df = pd.DataFrame(columns=['onset', 'duration', 'description'])
        
        logger.debug("All plots generated successfully")
        
    except Exception as e:
        logger.error(f"Plot generation failed: {e}")
        # Don't raise - partial plot failure shouldn't crash the app
        notify(state, "warning", f"Some plots failed to generate: {str(e)}")
# ---------------- Debug Actions ----------------
@monitor_performance
def toggle_debug_action(state, **kwargs):
    """Toggle debug mode"""
    global debug, show_debug_panel
    
    debug = not debug
    show_debug_panel = debug
    
    # Adjust logging levels
    level = logging.DEBUG if debug else logging.INFO
    logger.setLevel(level)
    logging.getLogger('mne').setLevel(logging.WARNING if not debug else logging.INFO)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    update_debug_info(state)
    
    mode = "enabled" if debug else "disabled"
    logger.info(f"Debug mode {mode}")
    notify(state, "info", f"Debug mode {mode}")
@monitor_performance
def clear_debug_logs_action(state, **kwargs):
    """Clear debug logs"""
    global debug_logs
    
    debug_handler.clear_logs()
    debug_logs = pd.DataFrame(columns=['timestamp', 'level', 'module', 'funcName', 'message'])
    update_debug_info(state)
    logger.info("Debug logs cleared")
    notify(state, "info", "Debug logs cleared")
@monitor_performance
def change_debug_level_action(state, **kwargs):
    """Change debug level"""
    levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR
    }
    
    if debug_level in levels:
        logger.setLevel(levels[debug_level])
        logger.info(f"Log level changed to {debug_level}")
        notify(state, "info", f"Log level set to {debug_level}")
        update_debug_info(state)
    else:
        logger.warning(f"Unknown debug level: {debug_level}")
@monitor_performance
def export_debug_logs_action(state, **kwargs):
    """Export debug logs to file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(tempfile.gettempdir(), f"EEG_Debug_{timestamp}.txt")
        
        system_info = get_system_info()
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("EEG Visualizer Debug Log\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Debug Level: {debug_level}\n")
            f.write(f"Memory Usage: {memory_usage}\n")
            f.write("\nSystem Information:\n")
            for key, value in system_info.items():
                f.write(f"  {key}: {value}\n")
            
            if raw:
                f.write(f"\nEEG Data Information:\n")
                f.write(f"  Channels: {len(raw.ch_names)}\n")
                f.write(f"  Sampling Rate: {raw.info['sfreq']} Hz\n")
                f.write(f"  Duration: {raw.times[-1]:.2f} s\n")
                f.write(f"  Samples: {raw.n_times}\n")
            
            f.write("\n" + "=" * 80 + "\n\n")
            
            for log in debug_handler.get_logs():
                f.write(f"[{log['timestamp']}] {log['level']} - "
                       f"{log['module']}.{log['funcName']}:{log.get('lineno', '?')} - "
                       f"{log['message']}\n")
        
        logger.info(f"Debug logs exported to: {log_file}")
        notify(state, "success", f"Debug logs exported: {log_file}")
        
    except Exception as e:
        error_msg = f"Failed to export debug logs: {str(e)}"
        logger.error(error_msg)
        notify(state, "error", error_msg)
# ---------------- GUI Page Definition ----------------
page = """
<|toggle|theme|>
# EEG Visualizer 🧠
<|layout|columns=1 1 1|gap=10px|>
<|file_selector|content={file}|on_action=load_file_action|label=Upload EEG File|extensions=.edf,.bdf,.fif,.set|>
<|{file}|text|label=Current File|>
<|Delete All|button|on_action=delete_data_action|active={not loading}|>
|>
<|layout|columns=1 1 1|gap=10px|>
<|{ref}|selector|lov=average;mastoid|label=Reference|>
<|{band}|slider|min=0.1|max=100|range=True|label=Band-pass Filter (Hz)|>
<|{strict}|selector|lov=low;medium;high|label=QA Strictness|>
|>
<|layout|columns=1 1|gap=10px|>
<|{picks}|selector|multiple=True|lov={raw.ch_names if raw else []}|label=Channels to Plot|>
<|{debug}|toggle|label=Debug Mode|on_action=toggle_debug_action|>
|>
<|layout|columns=1 1 1 1|gap=10px|>
<|Preprocess|button|on_action=preprocess_action|active={not loading and raw}|>
<|Run ICA|button|on_action=ica_action|active={not loading and raw}|>
<|Quality Check|button|on_action=qa_action|active={not loading and raw}|>
<|Export Report|button|on_action=report_action|active={not loading and raw}|>
|>
<|{status}|text|class_name=status-text|>
<|part|render={loading}|>
<|Loading...|text|>
|>
<|part|render={debug and show_debug_panel}|class_name=debug-panel|>
## 🔧 Debug Panel
<|layout|columns=1 1 1|gap=10px|>
<|{debug_level}|selector|lov=DEBUG;INFO;WARNING;ERROR|label=Log Level|on_action=change_debug_level_action|>
<|Clear Logs|button|on_action=clear_debug_logs_action|>
<|Export Logs|button|on_action=export_debug_logs_action|>
|>
**Memory Usage:** {memory_usage}
<|part|render={not performance_stats.empty}|>
**Performance Stats:**
<|{performance_stats}|table|columns=Metric;Value|>
|>
**Recent Debug Logs:**
<|{debug_logs}|table|columns=timestamp;level;module;funcName;message|page_size=15|>
|>
<|part|render={raw is not None}|class_name=main-content|>
## 📊 EEG Analysis
<|part|class_name=plot-container|>
### Raw EEG Trace
<|{plot_raw}|chart|>
|>
<|layout|columns=1 1|gap=20px|>
<|part|class_name=plot-container|>
### Power Spectral Density
<|{plot_psd}|chart|>
|>
<|part|class_name=plot-container|>
### Electrode Topography
<|part|render={plot_topo}|>
<|{plot_topo}|image|width=100%|>
|>
<|part|render={not plot_topo}|>
<|No topography available|text|>
|>
|>
|>
<|part|class_name=plot-container|>
### Band Power Analysis
<|{plot_bp}|chart|>
|>
<|part|render={ica is not None}|class_name=plot-container|>
### ICA Components
<|part|render={plot_ica}|>
<|{plot_ica}|image|width=100%|>
|>
<|part|render={not plot_ica}|>
<|ICA plot not available|text|>
|>
|>
<|part|render={not event_df.empty}|class_name=plot-container|>
### Event Timeline
<|{plot_events}|chart|>
### Event Details
<|{event_df}|table|columns=onset;duration;description|>
|>
<|part|render={not metrics.empty}|class_name=metrics-container|>
## 📋 Quality Assessment
<|{metrics}|table|columns=Metric;Value|>
|>
|>
"""

# ---------------- Enhanced CSS Styling ----------------
css_style = """
.debug-panel {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.main-content {
    margin-top: 20px;
}
.plot-container {
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid #555;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.metrics-container {
    background: rgba(20, 50, 20, 0.8);
    border: 1px solid #4a5d23;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.status-text {
    font-weight: bold;
    font-size: 1.1em;
    color: #00ff88;
    margin: 10px 0;
}
.error {
    color: #ff6b6b;
}
.warning {
    color: #feca57;
}
.success {
    color: #48dbfb;
}
/* Table styling */
.taipy-table {
    background: rgba(30, 30, 30, 0.9);
    border-radius: 6px;
}
.taipy-table th {
    background: rgba(50, 50, 50, 0.9);
    color: #fff;
}
.taipy-table td {
    border-bottom: 1px solid #444;
}
/* Button styling */
.taipy-button {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
}
.taipy-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
/* File selector styling */
.taipy-file_selector {
    border: 2px dashed #666;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s ease;
}
.taipy-file_selector:hover {
    border-color: #888;
}
/* Responsive design */
@media (max-width: 768px) {
    .plot-container {
        margin: 5px 0;
        padding: 10px;
    }
    
    .debug-panel {
        margin: 5px 0;
        padding: 10px;
    }
}
"""

# ---------------- Main Application ----------------
if __name__ == "__main__":
    try:
        logger.info("Starting Enhanced EEG Visualizer")
        logger.info(f"MNE version: {mne.__version__}")
        logger.info(f"Python version: {sys.version}")
        
        # Configure MNE logging
        mne.set_log_level('WARNING')
        
        # Initialize GUI with custom styling
        gui = Gui(page)
        
        # State variables are now defined at module level - no initialization needed
        
        logger.info("GUI initialized, starting server...")
        
        # Run the application
        gui.run(
            title="Enhanced EEG Visualizer - Debug Improved",
            port=5000,
            dark_mode=True,
            debug=False,  # Taipy debug, not our debug
            use_reloader=False,  # Prevent double startup in development
            host="127.0.0.1"
        )
        
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        logger.debug(f"Full traceback:\n{traceback.format_exc()}")
        print(f"Fatal error: {e}")
        raise
    finally:
        logger.info("EEG Visualizer shutdown complete")