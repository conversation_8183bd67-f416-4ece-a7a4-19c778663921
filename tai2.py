"""
EEG Visualizer – One-File Fixed & Improved
Author: you

Major fixes & upgrades:
- Fixed Taipy bindings (file selector now writes to `state.file`).
- Robust slicing & timing in raw plots (no float-index errors; uses `get_data`).
- Correct EEG units (µV) and PSD units (µV²/Hz) across the app.
- Safer, more meaningful QA metrics (band-limited SNR with proper 50 Hz noise band; flat channels via ptp; robust kurtosis; clear thresholds).
- Event handling compatible with `mne.Annotations` API.
- More reliable topography/ICA plotting using MNE viz helpers.
- PDF export resilient to missing Kaleido (skips Plotly images gracefully, still exports metrics & available images).
- Better logging, debug panel, and guards to prevent crashes.

Dependencies (Python >=3.9 recommended):
- mne, numpy, matplotlib, plotly, taipy-gui, fpdf2, qrcode[pil]
- Optional for Plotly image export in PDF: kaleido

Run:
    python app.py
"""
from __future__ import annotations

# ------------------------- stdlib -------------------------
import os, json, math, tempfile, time, base64, io, logging, traceback
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple

# ------------------------- third-party -------------------------
import numpy as np
import mne
from mne.preprocessing import ICA

import matplotlib
matplotlib.use("Agg")  # headless safe
import matplotlib.pyplot as plt

import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots

from taipy.gui import Gui, State, notify, Markdown
from fpdf import FPDF
import qrcode

# Try optional kaleido for Plotly static images
try:
    import kaleido  # noqa: F401
    _HAS_KALEIDO = True
except Exception:
    _HAS_KALEIDO = False

# ---------------- CONFIG ----------------
if os.path.exists("logo.svg"):
    with open("logo.svg", "rb") as f:
        LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(f.read()).decode()
else:
    svg = """<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100\" height=\"100\" viewBox=\"0 0 100 100\">
        <circle cx=\"50\" cy=\"50\" r=\"40\" stroke=\"#22c55e\" stroke-width=\"4\" fill=\"#fde047\" />
        <text x=\"50\" y=\"56\" font-size=\"24\" text-anchor=\"middle\" fill=\"#111827\">EEG</text>
    </svg>"""
    LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(svg.encode()).decode()

# ---------------- Logging with in-app buffer ----------------
class DebugHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.logs: List[Dict[str, Any]] = []
        self.max_logs = 1500
    def emit(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).strftime('%H:%M:%S.%f')[:-3],
            'level': record.levelname,
            'message': self.format(record),
            'module': record.module,
            'funcName': record.funcName,
            'lineno': record.lineno,
        }
        self.logs.append(log_entry)
        if len(self.logs) > self.max_logs:
            self.logs = self.logs[-self.max_logs:]
    def get_logs(self, level: Optional[str] = None):
        if level:
            return [log for log in self.logs if log['level'] == level]
        return list(self.logs)
    def clear_logs(self):
        self.logs.clear()

debug_handler = DebugHandler()
debug_handler.setFormatter(logging.Formatter('%(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s'))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[logging.StreamHandler(), debug_handler]
)
logger = logging.getLogger("EEGApp")

# ---------------- Perf decorator ----------------
def monitor_performance(func):
    def wrapper(*args, **kwargs):
        t0 = time.time()
        try:
            out = func(*args, **kwargs)
            dt = time.time() - t0
            logger.debug(f"{func.__name__} completed in {dt:.3f}s")
            return out
        except Exception as e:
            dt = time.time() - t0
            logger.error(f"{func.__name__} failed after {dt:.3f}s: {e}")
            logger.debug("Traceback:\n" + traceback.format_exc())
            raise
    return wrapper

# ---------------- Utilities ----------------
_DEF_LINE_FREQ = 50.0  # Hz for EU

@monitor_performance
def load_raw(path: str) -> mne.io.BaseRaw:
    ext = os.path.splitext(path)[1].lower()
    loaders = {
        ".edf": mne.io.read_raw_edf,
        ".bdf": mne.io.read_raw_bdf if hasattr(mne.io, "read_raw_bdf") else mne.io.read_raw_edf,
        ".fif": mne.io.read_raw_fif,
        ".set": mne.io.read_raw_eeglab,
    }
    if ext not in loaders:
        raise ValueError(f"Unsupported file type: {ext}")
    logger.info(f"Loading file: {path}")
    raw = loaders[ext](path, preload=True)
    logger.info(f"Loaded: {raw.info['nchan']} ch, {raw.n_times} samples, {raw.info['sfreq']} Hz, {raw.times[-1]:.2f}s")
    return raw

@monitor_performance
def preprocess(raw: mne.io.BaseRaw, ref: str, l: float, h: float) -> mne.io.BaseRaw:
    raw = raw.copy()
    logger.info(f"Preprocess: ref={ref} band={l:.2f}-{h:.2f} Hz")
    # referencing
    if ref == "average":
        raw.set_eeg_reference("average", projection=False)
    elif ref == "mastoid":
        try:
            raw.set_eeg_reference([ch for ch in ("M1","M2","TP9","TP10") if ch in raw.ch_names])
        except Exception as e:
            logger.warning(f"Mastoid ref failed ({e}); falling back to average")
            raw.set_eeg_reference("average", projection=False)
    # filtering (zero-phase FIR)
    raw.filter(l, h, method='fir', phase='zero-double', picks='eeg')
    return raw

@monitor_performance
def run_ica(raw: mne.io.BaseRaw, n_components: int = 20) -> Tuple[mne.io.BaseRaw, ICA]:
    logger.info(f"Running ICA (n_components={n_components})")
    ica = ICA(n_components=n_components, random_state=97, max_iter='auto')
    # It's usually recommended to high-pass >=1 Hz before ICA; assume user filtered.
    ica.fit(raw.copy().filter(1.0, None))
    cleaned = ica.apply(raw.copy())
    return cleaned, ica

# ---------------- Metrics (QA) ----------------
@monitor_performance
def _bandpower(psd: np.ndarray, freqs: np.ndarray, fmin: float, fmax: float) -> np.ndarray:
    mask = (freqs >= fmin) & (freqs < fmax)
    if not np.any(mask):
        return np.zeros(psd.shape[0])
    return psd[:, mask].mean(axis=1)

@monitor_performance
def compute_metrics(raw: mne.io.BaseRaw, strict: str) -> Dict[str, Any]:
    data_v = raw.get_data(picks='eeg')  # in Volts
    data_uv = data_v * 1e6
    sf = float(raw.info['sfreq'])

    # PSD (Welch) up to 90 Hz for noise estimates
    psds, freqs = mne.time_frequency.psd_welch(raw, fmin=0.5, fmax=min(90.0, sf/2.0))
    # Convert to µV^2/Hz
    psds_uv = psds * (1e6 ** 2)

    # band powers
    p_1_40 = _bandpower(psds_uv, freqs, 1.0, 40.0)
    p_line = _bandpower(psds_uv, freqs, _DEF_LINE_FREQ-1.0, _DEF_LINE_FREQ+1.0)
    p_hf   = _bandpower(psds_uv, freqs, 60.0, min(90.0, sf/2.0)) if freqs.max() >= 60 else np.zeros_like(p_1_40)
    noise_power = p_line + p_hf + 1e-12
    snr_chan = p_1_40 / noise_power
    snr_mean = float(np.median(snr_chan))

    # flat channels via peak-to-peak amplitude (µV)
    ptp_uv = data_uv.ptp(axis=1)
    n_flat = int(np.sum(ptp_uv < 5.0))  # <5 µV over recording -> suspiciously flat

    # kurtosis per channel (excess kurtosis; normal ~=0)
    x = data_uv - data_uv.mean(axis=1, keepdims=True)
    var = x.var(axis=1, ddof=1) + 1e-12
    m4 = (x**4).mean(axis=1)
    kurt_excess = m4 / (var**2) - 3.0
    n_high_kurt = int(np.sum(kurt_excess > 10.0))

    # thresholds by strictness
    thr = {
        "low":    dict(snr=2.0, flat=5, kurt=25, noise=50.0),
        "medium": dict(snr=5.0, flat=3, kurt=15, noise=25.0),
        "high":   dict(snr=10.0, flat=1, kurt=10, noise=15.0),
    }[strict]

    metrics: Dict[str, Any] = {
        "Median SNR (1–40Hz / noise)": round(snr_mean, 2),
        "Flat Channels (<5 µV ptp)": n_flat,
        "High Kurtosis (>10)": n_high_kurt,
        f"Line Noise @{int(_DEF_LINE_FREQ)}Hz (µV²/Hz)": round(float(np.median(p_line)), 2),
        "Pass SNR": snr_mean >= thr["snr"],
        "Pass Flat": n_flat <= thr["flat"],
        "Pass Kurtosis": n_high_kurt <= thr["kurt"],
        "Pass Line Noise": float(np.median(p_line)) <= thr["noise"],
        "Sampling Rate": f"{sf:.2f} Hz",
        "Duration": f"{raw.times[-1]:.2f} s",
        "N Channels": int(len(raw.ch_names)),
        "Data Range (µV)": f"[{data_uv.min():.1f}, {data_uv.max():.1f}]",
        "Data Std (µV)": float(data_uv.std()),
    }
    checks = [metrics["Pass SNR"], metrics["Pass Flat"], metrics["Pass Kurtosis"], metrics["Pass Line Noise"]]
    metrics["Overall Quality"] = "Good" if all(checks) else ("Fair" if sum(checks)>=2 else "Poor")
    metrics["Quality Score"] = f"{sum(checks)}/4"
    return metrics

# ---------------- QR helper ----------------
def qr_code(data: str) -> str:
    try:
        img = qrcode.make(data)
        buf = io.BytesIO()
        img.save(buf, format="PNG")
        return "data:image/png;base64," + base64.b64encode(buf.getvalue()).decode()
    except Exception as e:
        logger.error(f"QR generation failed: {e}")
        return ""

# ---------------- Plotting ----------------
@monitor_performance
def plot_raw(raw: mne.io.BaseRaw, picks: List[str], start: float = 0.0, window: float = 10.0, decim: int = 10):
    if not picks:
        picks = raw.ch_names[:min(10, len(raw.ch_names))]
    sf = float(raw.info['sfreq'])
    start_samp = int(max(0, start) * sf)
    stop_samp = int(min(raw.n_times, (start + window) * sf))
    data_v = raw.get_data(picks=picks, start=start_samp, stop=stop_samp)  # V
    data_uv = data_v * 1e6
    times = raw.times[start_samp:stop_samp]

    fig = make_subplots(rows=len(picks), cols=1, shared_xaxes=True, vertical_spacing=0.02)
    for i, (ch, ch_data) in enumerate(zip(picks, data_uv)):
        fig.add_trace(
            go.Scatter(
                x=times[::decim],
                y=ch_data[::decim],
                mode="lines",
                line=dict(width=1, color=px.colors.qualitative.Dark24[i % 24]),
                name=ch,
                hovertemplate=f"{ch}<br>t=%{{x:.2f}} s<br>amp=%{{y:.1f}} µV",
                showlegend=False,
            ),
            row=i+1, col=1,
        )
        fig.update_yaxes(title_text=ch, row=i+1, col=1)

    fig.update_layout(
        title=f"EEG Raw Trace ({start:.1f}–{start+window:.1f} s)",
        hovermode="closest",
        height=150 + 60 * len(picks),
        template="plotly_dark",
        xaxis=dict(title="Time (s)", rangeslider=dict(visible=True)),
        yaxis_title="µV",
    )
    return fig

@monitor_performance
def plot_psd(raw: mne.io.BaseRaw):
    psds, freqs = mne.time_frequency.psd_welch(raw, fmin=0.5, fmax=min(60.0, raw.info['sfreq']/2.0))
    psds_uv = psds * (1e6 ** 2)  # µV²/Hz
    fig = go.Figure()
    for i, ch in enumerate(raw.ch_names[:min(12, len(raw.ch_names))]):
        fig.add_trace(go.Scatter(
            x=freqs, y=psds_uv[i], name=ch, opacity=0.7,
            hovertemplate=f"{ch}<br>f=%{{x:.1f}} Hz<br>P=%{{y:.2e}} µV²/Hz",
        ))
    fig.update_layout(
        title="Power Spectral Density (Welch)",
        xaxis_title="Frequency (Hz)", yaxis_title="Power (µV²/Hz)",
        template="plotly_dark", height=420,
        legend=dict(orientation="h", yanchor="bottom", y=1.02)
    )
    return fig

@monitor_performance
def plot_topo(raw: mne.io.BaseRaw) -> Optional[str]:
    try:
        fig = mne.viz.plot_sensors(raw.info, kind='topomap', show=False)
        buf = io.BytesIO()
        fig.savefig(buf, format="png", dpi=150, transparent=True, bbox_inches='tight')
        plt.close(fig)
        return f"data:image/png;base64,{base64.b64encode(buf.getvalue()).decode()}"
    except Exception as e:
        logger.error(f"Topography plot failed: {e}")
        return None

@monitor_performance
def plot_bandpower(raw: mne.io.BaseRaw):
    psds, freqs = mne.time_frequency.psd_welch(raw, fmin=0.5, fmax=min(40.0, raw.info['sfreq']/2.0))
    psds_uv = psds * (1e6 ** 2)
    bands = {
        "Delta": (0.5, 4),
        "Theta": (4, 8),
        "Alpha": (8, 13),
        "Beta":  (13, 30),
        "Gamma": (30, 40),
    }
    fig = go.Figure()
    for name, (fmin, fmax) in bands.items():
        mask = (freqs >= fmin) & (freqs < fmax)
        power = psds_uv[:, mask].mean(axis=1)
        fig.add_trace(go.Bar(name=name, x=raw.ch_names, y=power, opacity=0.8))
    fig.update_layout(
        title="Band Power by Channel (µV²/Hz, mean in band)",
        barmode='group', template="plotly_dark", height=430,
        xaxis_title="Channel", yaxis_title="Power (µV²/Hz)"
    )
    return fig

@monitor_performance
def plot_ica(ica: ICA, raw: mne.io.BaseRaw) -> Optional[str]:
    try:
        figs = ica.plot_components(show=False)
        # MNE may return a single fig or list
        fig = figs if hasattr(figs, 'savefig') else figs[0]
        buf = io.BytesIO()
        fig.savefig(buf, format="png", dpi=120, transparent=True, bbox_inches='tight')
        plt.close(fig)
        return f"data:image/png;base64,{base64.b64encode(buf.getvalue()).decode()}"
    except Exception as e:
        logger.error(f"ICA plot failed: {e}")
        return None

@monitor_performance
def plot_events(raw: mne.io.BaseRaw):
    anns = raw.annotations
    if anns is None or len(anns) == 0:
        return None
    times = np.array(anns.onset)
    durs = np.array(anns.duration)
    desc = list(anns.description)

    fig = go.Figure()
    for i, (t, d, de) in enumerate(zip(times, durs, desc)):
        fig.add_trace(go.Scatter(
            x=[t, t + d], y=[i, i], mode="lines+markers", line=dict(width=8), name=str(de),
            hovertemplate=f"Event: {de}<br>Start: %{{x:.2f}} s<br>Dur: {d:.2f} s",
        ))
    fig.update_layout(title="Event Timeline", xaxis_title="Time (s)", yaxis_title="Events",
                      template="plotly_dark", height=320, showlegend=True)
    return fig

# ---------------- PDF REPORT ----------------
class PDF(FPDF):
    def header(self):
        if LOGO_B64:
            try:
                img_data = LOGO_B64.split(',')[1] if ',' in LOGO_B64 else LOGO_B64
                img_bytes = base64.b64decode(img_data)
                tmp = os.path.join(tempfile.gettempdir(), "_eeg_logo.png")
                with open(tmp, "wb") as f:
                    f.write(img_bytes)
                try:
                    self.image(tmp, 10, 8, 20)
                finally:
                    try: os.remove(tmp)
                    except Exception: pass
            except Exception as e:
                logger.debug(f"Logo failed: {e}")
        self.set_font("Helvetica", "B", 14)
        self.cell(0, 10, "EEG Quality Report", 0, 1, "C")
        self.set_font("Helvetica", "I", 10)
        self.cell(0, 8, f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 0, 1, "C")
    def footer(self):
        self.set_y(-15)
        self.set_font("Helvetica", "I", 8)
        self.cell(0, 10, f"Page {self.page_no()}", 0, 0, "C")
    def add_plot_image(self, title: str, img_path_or_b64: str):
        self.add_page()
        self.set_font("Helvetica", "B", 12)
        self.cell(0, 10, title, ln=True)
        try:
            if img_path_or_b64.startswith("data:image/"):
                img_bytes = base64.b64decode(img_path_or_b64.split(',')[1])
                tmp = os.path.join(tempfile.gettempdir(), f"_plot_{hash(title)}.png")
                with open(tmp, 'wb') as f: f.write(img_bytes)
                self.image(tmp, x=10, y=30, w=190)
                try: os.remove(tmp)
                except Exception: pass
            else:
                self.image(img_path_or_b64, x=10, y=30, w=190)
        except Exception as e:
            logger.error(f"PDF add plot failed ({title}): {e}")

@monitor_performance
def export_report(raw: mne.io.BaseRaw, metrics: Dict[str, Any], dest: str) -> str:
    pdf = PDF()
    pdf.add_page()
    pdf.set_font("Helvetica", "B", 12)
    pdf.cell(0, 10, "Quality Metrics", ln=True, align="C")
    pdf.set_font("Helvetica", "", 11)
    pdf.ln(2)
    for k, v in metrics.items():
        pdf.cell(0, 7, f"{k}: {v}", ln=True)

    # Build plots now
    try:
        raw_fig = plot_raw(raw, raw.ch_names[:min(10, len(raw.ch_names))])
        psd_fig = plot_psd(raw)
        topo_img = plot_topo(raw)
        bp_fig = plot_bandpower(raw)
    except Exception as e:
        logger.error(f"Plot preparation failed: {e}")
        raw_fig = psd_fig = bp_fig = None
        topo_img = None

    def _save_plotly(fig, name: str) -> Optional[str]:
        if fig is None:
            return None
        out_path = os.path.join(tempfile.gettempdir(), f"{name}.png")
        try:
            if _HAS_KALEIDO:
                fig.write_image(out_path, format="png")
                return out_path
            else:
                logger.warning("Kaleido not available; skipping Plotly image in PDF")
                return None
        except Exception as e:
            logger.error(f"Plotly export failed ({name}): {e}")
            return None

    for title, fig in (("Raw EEG Trace", raw_fig), ("Power Spectral Density", psd_fig), ("Band Power", bp_fig)):
        pth = _save_plotly(fig, title.replace(' ', '_'))
        if pth:
            pdf.add_plot_image(title, pth)
    if topo_img:
        pdf.add_plot_image("Electrode Topography", topo_img)

    # QR to file location (useful locally)
    pdf.add_page()
    pdf.set_font("Helvetica", "B", 12)
    pdf.cell(0, 10, "Scan to open data (local path)", ln=True, align="C")
    qr_img = qr_code("file://" + dest)
    if qr_img:
        try:
            tmp = os.path.join(tempfile.gettempdir(), "_qr.png")
            with open(tmp, 'wb') as f: f.write(base64.b64decode(qr_img.split(',')[1]))
            pdf.image(tmp, x=80, y=50, w=50)
            try: os.remove(tmp)
            except Exception: pass
        except Exception as e:
            logger.error(f"QR insertion failed: {e}")

    pdf.output(dest)
    logger.info(f"PDF saved: {dest}")
    return dest

# ---------------- TAIPY STATE ----------------
class EEGState(State):
    file: str = ""
    raw: Optional[mne.io.BaseRaw] = None
    ica: Optional[ICA] = None
    ref: str = "average"
    band: List[float] = [1.0, 40.0]
    strict: str = "medium"
    picks: List[str] = []
    metrics: Dict[str, Any] = {}
    loading: bool = False
    progress: int = 0
    debug: bool = False
    show_debug_panel: bool = False
    debug_logs: List[Dict[str, Any]] = []
    debug_level: str = "INFO"
    memory_usage: str = ""
    performance_stats: List[Dict[str, Any]] = []  # renderable

    # plot holders
    plot_raw: Any = {}
    plot_psd: Any = {}
    plot_topo: str = ""
    plot_bp: Any = {}
    plot_ica: str = ""
    plot_events: Any = {}
    event_df: List[Dict[str, Any]] = []

    status: str = "Ready"

# ---------------- Debug Helpers ----------------
def get_memory_usage() -> str:
    try:
        import psutil
        process = psutil.Process(os.getpid())
        return f"{process.memory_info().rss / 1024 / 1024:.1f} MB"
    except Exception:
        return "N/A"

def update_debug_info(state: EEGState):
    if state.debug:
        state.debug_logs = debug_handler.get_logs()[-200:]
        state.memory_usage = get_memory_usage()
        if state.raw is not None:
            stat_rows = [
                {"Metric": "Channels", "Value": len(state.raw.ch_names)},
                {"Metric": "Samples", "Value": int(state.raw.n_times)},
                {"Metric": "Duration", "Value": f"{state.raw.times[-1]:.2f}s"},
                {"Metric": "Sampling Rate", "Value": f"{state.raw.info['sfreq']:.2f} Hz"},
                {"Metric": "Memory", "Value": state.memory_usage},
            ]
            state.performance_stats = stat_rows

# ---------------- Actions ----------------
@monitor_performance
def report_action(state: EEGState):
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    try:
        state.loading = True
        state.status = "Generating report…"
        if not state.metrics:
            state.metrics = compute_metrics(state.raw, state.strict)
        dest = os.path.join(tempfile.gettempdir(), f"EEG_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        export_report(state.raw, state.metrics, dest)
        state.status = "Report generated"
        notify(state, "success", f"Report saved: {dest}")
    except Exception as e:
        notify(state, "error", f"Report failed: {e}")
        state.status = "Error generating report"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def plot_all(state: EEGState):
    if not state.raw:
        return
    try:
        state.plot_raw = plot_raw(state.raw, state.picks[:15] if state.picks else state.raw.ch_names[:15])
        state.plot_psd = plot_psd(state.raw)
        state.plot_topo = plot_topo(state.raw) or ""
        state.plot_bp = plot_bandpower(state.raw)
        state.plot_events = plot_events(state.raw) or {}
        anns = state.raw.annotations
        if anns is not None and len(anns) > 0:
            state.event_df = [
                {"onset": float(o), "duration": float(d), "description": str(de)}
                for o, d, de in zip(anns.onset, anns.duration, anns.description)
            ]
        else:
            state.event_df = []
    except Exception as e:
        logger.error(f"Plotting failed: {e}")
        notify(state, "error", f"Plotting failed: {e}")

@monitor_performance
def toggle_debug(state: EEGState):
    state.debug = not state.debug
    state.show_debug_panel = state.debug
    level = logging.DEBUG if state.debug else logging.INFO
    logger.setLevel(level)
    logging.getLogger('mne').setLevel(logging.WARNING if not state.debug else logging.INFO)
    notify(state, "info", f"Debug mode {'enabled' if state.debug else 'disabled'}")
    update_debug_info(state)

@monitor_performance
def clear_debug_logs(state: EEGState):
    debug_handler.clear_logs()
    state.debug_logs = []
    notify(state, "info", "Debug logs cleared")

@monitor_performance
def change_debug_level(state: EEGState):
    levels = {"DEBUG": logging.DEBUG, "INFO": logging.INFO, "WARNING": logging.WARNING, "ERROR": logging.ERROR}
    if state.debug_level in levels:
        logger.setLevel(levels[state.debug_level])
        notify(state, "info", f"Log level set to {state.debug_level}")
        update_debug_info(state)

@monitor_performance
def export_debug_logs(state: EEGState):
    try:
        ts = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(tempfile.gettempdir(), f"EEG_Debug_{ts}.txt")
        with open(log_file, 'w') as f:
            f.write("EEG Visualizer Debug Log\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Debug Level: {state.debug_level}\n")
            f.write(f"Memory Usage: {state.memory_usage}\n")
            f.write("="*80 + "\n\n")
            for log in debug_handler.get_logs():
                f.write(f"[{log['timestamp']}] {log['level']} - {log['module']}.{log['funcName']}:{log['lineno']} - {log['message']}\n")
        notify(state, "success", f"Debug logs exported: {log_file}")
    except Exception as e:
        notify(state, "error", f"Export failed: {e}")

# File load / delete / preprocess / ICA / QA
@monitor_performance
def load(state: EEGState):
    if not state.file:
        notify(state, "warning", "Please select a file")
        return
    try:
        state.loading = True
        state.status = "Loading file…"
        raw = load_raw(state.file)
        state.raw = raw
        state.picks = list(raw.ch_names)
        state.status = "File loaded"
        plot_all(state)
        update_debug_info(state)
        notify(state, "success", "File loaded successfully")
    except Exception as e:
        notify(state, "error", f"Load failed: {e}")
        state.status = "Error loading file"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def delete(state: EEGState):
    state.file = ""
    state.raw = None
    state.ica = None
    state.picks = []
    state.metrics = {}
    state.plot_raw = {}
    state.plot_psd = {}
    state.plot_topo = ""
    state.plot_bp = {}
    state.plot_ica = ""
    state.plot_events = {}
    state.event_df = []
    state.performance_stats = []
    state.status = "Ready"
    update_debug_info(state)
    notify(state, "info", "Data cleared")

@monitor_performance
def preprocess_action(state: EEGState):
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    try:
        state.loading = True
        state.status = "Preprocessing…"
        state.raw = preprocess(state.raw, state.ref, *state.band)
        state.status = "Preprocessing complete"
        plot_all(state)
        update_debug_info(state)
        notify(state, "success", "Preprocessing complete")
    except Exception as e:
        notify(state, "error", f"Preprocessing failed: {e}")
        state.status = "Error in preprocessing"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def ica_action(state: EEGState):
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    try:
        state.loading = True
        state.status = "Running ICA…"
        state.raw, state.ica = run_ica(state.raw)
        state.plot_ica = plot_ica(state.ica, state.raw) or ""
        state.status = "ICA complete"
        plot_all(state)
        update_debug_info(state)
        notify(state, "success", "ICA decomposition complete")
    except Exception as e:
        notify(state, "error", f"ICA failed: {e}")
        state.status = "Error in ICA"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def qa_action(state: EEGState):
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    try:
        state.loading = True
        state.status = "Computing metrics…"
        state.metrics = compute_metrics(state.raw, state.strict)
        state.status = "Metrics computed"
        update_debug_info(state)
        if state.metrics.get("Overall Quality") in ("Good", "Fair"):
            notify(state, "success", f"Data quality {state.metrics['Overall Quality']} ({state.metrics['Quality Score']})")
        else:
            notify(state, "warning", f"Data quality {state.metrics['Overall Quality']} ({state.metrics['Quality Score']})")
    except Exception as e:
        notify(state, "error", f"QA failed: {e}")
        state.status = "Error in QA"
    finally:
        state.loading = False
        update_debug_info(state)

# ---------------- GUI (Taipy markdown page) ----------------
page = r"""
<|toggle|theme|>
# EEG Visualizer 🧠

<|layout|columns=1 1 1|gap=10px|>
<|file_selector|file|on_action=load|label=Upload EEG|extensions=.edf,.bdf,.fif,.set|>
<|{file}|text|label=Current File|>
<|Delete|button|on_action=delete|active={not loading}|>
<|layout|>

<|layout|columns=1 1 1|gap=10px|>
<|{ref}|selector|lov=average;mastoid|label=Reference|>
<|{band}|slider|min=0.1|max=100|range|label=Band-pass (Hz)|>
<|{strict}|selector|lov=low;medium;high|label=QA Strictness|>
<|layout|>

<|layout|columns=1 1|gap=10px|>
<|{picks}|selector|multiple|lov={raw.ch_names if raw else []}|label=Channels|>
<|{debug}|toggle|label=Debug Mode|on_change=toggle_debug|>
<|layout|>

<|layout|columns=1 1 1 1|gap=10px|>
<|Preprocess|button|on_action=preprocess_action|active={not loading and raw}|>
<|ICA|button|on_action=ica_action|active={not loading and raw}|>
<|QA|button|on_action=qa_action|active={not loading and raw}|>
<|Export PDF|button|on_action=report_action|active={not loading and raw}|>
<|layout|>

<|{status}|text|style=font-weight:bold;|>
<|{loading}|spinner|>

<|part|render={debug and show_debug_panel}|class_name=card|
## Debug Panel 🔧
<|layout|columns=1 1 1|gap=10px|>
<|{debug_level}|selector|lov=DEBUG;INFO;WARNING;ERROR|label=Log Level|on_change=change_debug_level|>
<|Clear Logs|button|on_action=clear_debug_logs|>
<|Export Logs|button|on_action=export_debug_logs|>
<|layout|>

**Memory Usage:** {memory_usage}

<|part|render={len(performance_stats) > 0}|>
**Performance Stats:**
<|{performance_stats}|table|>
|>

**Recent Debug Logs:**
<|{debug_logs}|table|columns=timestamp;level;module;funcName;message|page_size=10|>
|>

<|part|class_name=card|>
## Raw EEG Trace
<|{plot_raw}|chart|>
|>

<|layout|columns=1 1|gap=20px|>
<|part|class_name=card|>
## Power Spectral Density
<|{plot_psd}|chart|>
|>
<|part|class_name=card|>
## Electrode Topography
<|{plot_topo}|image|width=100%|>
|>
<|layout|>

<|part|class_name=card|>
## Band Power
<|{plot_bp}|chart|>
|>

<|part|class_name=card|render={ica is not None}|>
## ICA Components
<|{plot_ica}|image|width=100%|>
|>

<|part|class_name=card|render={len(event_df) > 0}|>
## Event Timeline
<|{plot_events}|chart|>
## Event Details
<|{event_df}|table|>
|>

<|part|class_name=card|render={metrics}|>
## Quality Metrics
<|{metrics}|table|>
|>
"""

# ---------------- Main ----------------
if __name__ == "__main__":
    try:
        logger.info("Starting EEG Visualizer…")
        gui = Gui(page)
        gui.run(title="EEG Visualizer – Fixed & Improved", port=5000, dark_mode=True)
    except Exception as e:
        logger.error(f"Failed to start GUI: {e}")
        print(f"Error starting application: {e}")
        raise
