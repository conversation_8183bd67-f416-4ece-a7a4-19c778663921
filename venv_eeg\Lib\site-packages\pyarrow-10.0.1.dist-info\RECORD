../../Scripts/plasma_store.exe,sha256=IN1uzDX_Bpw_nVxuwHxrqi_B0d7_GgP5VixFMjyOZTU,106393
pyarrow-10.0.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyarrow-10.0.1.dist-info/LICENSE.txt,sha256=BVzlhiQgv4VXqqwocBkZ5jU-PuuYrlZgrKDUb1nCkMQ,115978
pyarrow-10.0.1.dist-info/METADATA,sha256=BCVrul-VjKUMmq3JlG4xVXjf_60-9RFOjLJfP_bP-YE,3124
pyarrow-10.0.1.dist-info/NOTICE.txt,sha256=On44PMyhL72zMqEVO7hvOHx7zl_T0RAqsmpVg07r27Y,3116
pyarrow-10.0.1.dist-info/RECORD,,
pyarrow-10.0.1.dist-info/WHEEL,sha256=rneS2j8QNmAwdNKHN86s6-qP7AMcWZgqiEoH3bdbh_Y,102
pyarrow-10.0.1.dist-info/entry_points.txt,sha256=g3H1iRY9gsr1bFFmPZFS4N-dBoQb7Kli-CGYGP9HE-g,67
pyarrow-10.0.1.dist-info/top_level.txt,sha256=Zuk_c1WeinXdMz20fXlEtGC67zfKOWuwU8adpEEU_nI,18
pyarrow/__init__.pxd,sha256=eC5b2a7fm-SmomDxOM02JS_5jrwnBTFc1tzxA7nLrYM,2237
pyarrow/__init__.py,sha256=Zz2D_MUt3pl01bbIKH1WawTeFShBozhQNcjO09Ysj28,20437
pyarrow/__pycache__/__init__.cpython-310.pyc,,
pyarrow/__pycache__/_compute_docstrings.cpython-310.pyc,,
pyarrow/__pycache__/_generated_version.cpython-310.pyc,,
pyarrow/__pycache__/benchmark.cpython-310.pyc,,
pyarrow/__pycache__/cffi.cpython-310.pyc,,
pyarrow/__pycache__/compute.cpython-310.pyc,,
pyarrow/__pycache__/conftest.cpython-310.pyc,,
pyarrow/__pycache__/csv.cpython-310.pyc,,
pyarrow/__pycache__/cuda.cpython-310.pyc,,
pyarrow/__pycache__/dataset.cpython-310.pyc,,
pyarrow/__pycache__/feather.cpython-310.pyc,,
pyarrow/__pycache__/filesystem.cpython-310.pyc,,
pyarrow/__pycache__/flight.cpython-310.pyc,,
pyarrow/__pycache__/fs.cpython-310.pyc,,
pyarrow/__pycache__/hdfs.cpython-310.pyc,,
pyarrow/__pycache__/ipc.cpython-310.pyc,,
pyarrow/__pycache__/json.cpython-310.pyc,,
pyarrow/__pycache__/jvm.cpython-310.pyc,,
pyarrow/__pycache__/orc.cpython-310.pyc,,
pyarrow/__pycache__/pandas_compat.cpython-310.pyc,,
pyarrow/__pycache__/plasma.cpython-310.pyc,,
pyarrow/__pycache__/serialization.cpython-310.pyc,,
pyarrow/__pycache__/substrait.cpython-310.pyc,,
pyarrow/__pycache__/types.cpython-310.pyc,,
pyarrow/__pycache__/util.cpython-310.pyc,,
pyarrow/_compute.cp310-win_amd64.pyd,sha256=AorhCh25ELNebw8DlnXXrJ3C9m7Hq716mBcZedJw9FA,612352
pyarrow/_compute.pxd,sha256=9bOwprDmfbiTWZLFw6MVsIFn_drxydAQhBbNZh22gFg,1803
pyarrow/_compute.pyx,sha256=dpFI0ClksorV1yv1dsFqUMJq6gXIubnEFJBXo_6b3BY,87562
pyarrow/_compute_docstrings.py,sha256=2KRhcMXk12GS_zihOVWsIjyU9tgPBpPNHUi-GpQPZ6I,1763
pyarrow/_csv.cp310-win_amd64.pyd,sha256=0OTf6QyygSOddgqpWb-hkhfgSYOqNdLrdYFOFb3sylQ,213504
pyarrow/_csv.pxd,sha256=rI6eCBX4P1-C6wwhRySHK3vWvjy2dorVeAPaSdROeZQ,1693
pyarrow/_csv.pyx,sha256=-74duoHvdHc_Mqy_7NQlK9XOT44zVcMjyO_QyfJNjvU,53419
pyarrow/_cuda.pxd,sha256=S9wJXbxxM8MO-oAgWHkblKMdQr9zuGoNfKGkIKVXw0c,1989
pyarrow/_cuda.pyx,sha256=7bHxd2mJnlfghteuZ5gW8gAiT8912V3RsdHeaklKhFw,35791
pyarrow/_dataset.cp310-win_amd64.pyd,sha256=L5sl39ggg99k3bpmuONanoHgZlmpWc8ipz0EOBSgRNo,486912
pyarrow/_dataset.pxd,sha256=6uK032S1j1ukJWwK1ePTdVgFZBRq8cTvaW1uQXPS0ZM,4534
pyarrow/_dataset.pyx,sha256=nNT3PsQ2oR8OMT0iqT291-vDbAFafPutomtV7C8EYbM,104072
pyarrow/_dataset_orc.pyx,sha256=yZ4X1ApniutFI0mkyIOL1xzidk22HDsysZw3DJbkdTw,1387
pyarrow/_dataset_parquet.cp310-win_amd64.pyd,sha256=f5ZFcNr7Tt1h3dibdw4IcaKTt8tzcl1XPpE_4b-jb1Q,201728
pyarrow/_dataset_parquet.pyx,sha256=vKF0thn1vzHG0hf87sI0gYqXq-p29VyUj_CymCMrDjQ,31883
pyarrow/_exec_plan.cp310-win_amd64.pyd,sha256=81tDY9Ifh47hjHAJGpX4qYu5YfiFTut2OAaQkqRHyKU,128000
pyarrow/_exec_plan.pyx,sha256=cYYQgyzWPLzWOtMnPryTYw1Hq87aY9cvgToz1mZF1JQ,16360
pyarrow/_feather.cp310-win_amd64.pyd,sha256=9AxHF3q_uxKzaqm86WfHzMe5X90W1iA1kbjlBzC1Os4,60928
pyarrow/_feather.pyx,sha256=uMhjMFDUvcfTVBaPCoydbmcRtbzP7Rz6C3v7T9bahGc,3890
pyarrow/_flight.cp310-win_amd64.pyd,sha256=RgPdd_I5sywyiAWTYv02Hr3Z8_qkhU54YggIYLuhD84,668672
pyarrow/_flight.pyx,sha256=VquVUC266_c_Hq4oETjKLsuL2L3TvOJszCU7I-dTiVM,110201
pyarrow/_fs.cp310-win_amd64.pyd,sha256=vAyM52k12ell3TMAhWoqUBzA9wvgUkkXzRtBpktqPeE,315904
pyarrow/_fs.pxd,sha256=rQcNU--vwgAqLIqmNzWEOdblEFpbCwhS-u05-yfflNA,2578
pyarrow/_fs.pyx,sha256=QnGIhpm-IcWhd-rwjt3d0Ocp4-d6FJahK-6_WwdQ8GU,54043
pyarrow/_gcsfs.pyx,sha256=3pz-CkC1S4X_2GKu9JyLBOacI_pERqPqIlu2fvntTsw,8343
pyarrow/_generated_version.py,sha256=EDJwsrXsnLokURzcTXm91lYn-ngd-6KAoUqk_roWgJI,183
pyarrow/_hdfs.cp310-win_amd64.pyd,sha256=lcoc8TTR9sNNpIla2XmuiVVqVbOP2Kr69n6SOR940Yo,71168
pyarrow/_hdfs.pyx,sha256=yGXhkMiuRN64gltechRU2wNYodaH6mLhvl_9xNHWFUo,5875
pyarrow/_hdfsio.cp310-win_amd64.pyd,sha256=ui2XAgNRlrZ7Zb-k-0Er3oVMOMI2X7sYj_Zf2w0w6wQ,129024
pyarrow/_hdfsio.pyx,sha256=HqJhDd6LvfMxMDpd08aZaFJ-b_5ZJoQ7uCvdNnXyLtI,14161
pyarrow/_json.cp310-win_amd64.pyd,sha256=eMEzi2e1_AAwZ_ZdzFa3pnVUe8WO-tuccERYGbQtsZA,53248
pyarrow/_json.pyx,sha256=_p1S_jY5v79rP6ZAB_KjPAFmJexqJpGEV9l1cZApVVg,8974
pyarrow/_orc.pxd,sha256=G5jSZUTqyt_stuKn0y56L9lyG8oRqf8NzMtzYSD1qaw,5823
pyarrow/_orc.pyx,sha256=7dE5o_E-U13wFOfcC8yQpQEvUqR9Uib35smmixR4mUg,16175
pyarrow/_parquet.cp310-win_amd64.pyd,sha256=GgWlXRqZVC2N06WMyttmB-vpa9504HZbaNGdbxpRRFA,287232
pyarrow/_parquet.pxd,sha256=o5zyF3ENAW4_aKudEyQsmP8mYJKqpcPCwkUt8VAQtJM,25944
pyarrow/_parquet.pyx,sha256=wMcMIKaRjOKK5toKv_c8URFqIEZ76VUoBeenqXDBIms,60229
pyarrow/_parquet_encryption.cp310-win_amd64.pyd,sha256=spIz_lXWEi36h5UOJP4VD81_u_dJLlE4P6F1HA0HBZk,143872
pyarrow/_parquet_encryption.pxd,sha256=isGsBPV6V1MOJahYoXZVuBRnU--zvZYCaB2vP38Gx10,6174
pyarrow/_parquet_encryption.pyx,sha256=EEQkJmoWC1yU-o-iSoE6vPYmFjpSmT5LFiA_QIWpUF8,18011
pyarrow/_plasma.pyx,sha256=CJeWU9eGHYqW3GResz2S-9iNSKQvADK570RdrKvEUnQ,31384
pyarrow/_pyarrow_cpp_tests.cp310-win_amd64.pyd,sha256=JZ4msTX74Nqq2fC0KMCKhrvWWLYGYqfxcGvRzmNdszo,55296
pyarrow/_pyarrow_cpp_tests.pxd,sha256=L_cgXkO1M54NUhRpzHPqLA3-h5qkqTjcxt_OfSlF01I,1232
pyarrow/_pyarrow_cpp_tests.pyx,sha256=cXM3tGxJyLqr-U2oqTKzdWVEsr7d7FLbrSzjw6Aagl0,1815
pyarrow/_s3fs.cp310-win_amd64.pyd,sha256=n39JqnG7-BfFAdeZoI8tDvy4Y6sOq0t1OkmEUvLtXMo,126464
pyarrow/_s3fs.pyx,sha256=3xO5jpJKXg1xNUtFE0oeN2dNGNdj0wfg1zrr2KChUlg,16779
pyarrow/_substrait.cp310-win_amd64.pyd,sha256=N4L_e6SarhYoYB6J4kovvBcbAJRQXtVuqSLkHPF3OYY,73216
pyarrow/_substrait.pyx,sha256=Jaj0p-1S491JvSAfV8oiQm59PFJ0Bcn3V2b5NBfiClc,6686
pyarrow/array.pxi,sha256=JkG22hkpUK-612LKV-7KgQ6J3LHhpcMpkwYJajjgeP0,95645
pyarrow/arrow.dll,sha256=9-tLPL0_4JPSqWcYpyCF_s4HU9--5pX7fqAOtigpFgs,22557184
pyarrow/arrow.lib,sha256=vQ1_EqUe0S8Qu_CtR1m2GQcgctscbKxopSX3MrJO-sQ,3518708
pyarrow/arrow_dataset.dll,sha256=-3s26GrTn3Q0muaspmR4P39M6NQKYsGB428nCK4OK8s,1168384
pyarrow/arrow_dataset.lib,sha256=wQeF_dccNp0v0DPVznWIDkTCG3kz7GaBoUjO_F8wrho,366802
pyarrow/arrow_flight.dll,sha256=bu8bbbELzmza3kRvPjkDvD8Q9jiL5mryS7yURK1j_eo,9537024
pyarrow/arrow_flight.lib,sha256=tZx4n15Re1jt_eY2limY810cxgyFbt2c9gDJbdCxCsI,299416
pyarrow/arrow_python.dll,sha256=1DSo-OozRdaiTe8zPDNlJih1EaMbA0Z1wYHmQJmXgCI,1143808
pyarrow/arrow_python.lib,sha256=TYQfhL0RIZ07_Y2CmV3RQ11GqTXHDnzx3M8asNwLDY8,176124
pyarrow/arrow_python_flight.dll,sha256=XpczUJefJS_TdDyXHbt7de64XhPjJtwnp1HuE6SlOG0,69632
pyarrow/arrow_python_flight.lib,sha256=h9sQmx7iybMxYQhRDY8ej97hWRFJQ1lxKAblreNqQCY,58738
pyarrow/arrow_substrait.dll,sha256=27ncZrEIlaqiqOFE0Y_BWWKDR8zVooHYkEax1sJhSSU,2236928
pyarrow/arrow_substrait.lib,sha256=gMDip7ayI1cH7Or0pwOVcMxz4vN-JZDOCqjGlIslUl0,57142
pyarrow/benchmark.pxi,sha256=mrrnfpGQcc4Dxw7EOo7vqzxW8NBcQ3DmPIAvp76U4aM,889
pyarrow/benchmark.py,sha256=i-yvjV5wmnxC-IMo1xAjys6mmM2ea1yyys6m7FzJt6g,877
pyarrow/builder.pxi,sha256=PC8fGuQ8D4k3mAMlXs8IQ9zmX4k98IiHnPhgqgcoXwE,2770
pyarrow/cffi.py,sha256=cU7kBIqCDDPdGY5IjGzOl2mXiwbw-bOsnex6TeCeog8,2249
pyarrow/compat.pxi,sha256=qx84ryXwiop725VbIkLQajGRU9wpzEiyL3bFmv9QpLk,2107
pyarrow/compute.py,sha256=9IhdazLAobu1txBwkT_kVyQBNpLIiX5UOoMwOnAw2vU,22490
pyarrow/config.pxi,sha256=A8Mcw9QKQPe8wyqDbXsf6h7QzH22z9RYZoZlfQ3TarA,2796
pyarrow/conftest.py,sha256=9XcHXgaKP3fcRdWPJoKrBiERyJtQuIQLgsCYQ22JPpY,6589
pyarrow/csv.py,sha256=YdN28bUXVQVe03NbWzJxT6P_mf43pv0pbv463MJQ-Wc,996
pyarrow/cuda.py,sha256=NJdOLaIiUFOnTIeRCaxNttBJDqwe-hbSdww5ls5xSJM,1112
pyarrow/dataset.py,sha256=C1tZgb_gfy-YT-jcj1djI8p2899hixP05zp1Pjl2uiM,39610
pyarrow/error.pxi,sha256=MIMe6UqYudCCtHVLwmFL844mTYIfO7NwGLW1HiAbnFw,8795
pyarrow/feather.py,sha256=M-Rcd0LaN0MVQlorMPAh7hZWOoJonVRIdxHRsVUY32g,10275
pyarrow/filesystem.py,sha256=r9YsZq-IjpAglicuYZ9nVDjUIdTM3WXdfWBA0A7nv3E,14979
pyarrow/flight.py,sha256=vhc6xbULaNkrblrSmny0jDHh0Mv_lBpK7nIpea014BQ,1894
pyarrow/fs.py,sha256=xZjTmqVa02AaordhJltbrXK8msbRKtVfzhnvV7bgaf0,15039
pyarrow/gandiva.pyx,sha256=xZGZ4ko6O4AZpYzGPyaD4VrdGdkTjsBL3BT5Viy6jLs,20798
pyarrow/hdfs.py,sha256=-RKHyuJ1jjpMmMGs0FleBSmPcBhcnTgculd87U8aPWs,7767
pyarrow/include/arrow/adapters/tensorflow/convert.h,sha256=PBuoNq4ASqWeVqi9cjrTTBc_MOn_FrFUH2uosRg8PIk,3593
pyarrow/include/arrow/api.h,sha256=xFMq_NmsgeUMLCp7wWkRSNA4f_paqiQbHUq24uGRD94,2470
pyarrow/include/arrow/array.h,sha256=dxsb1HyjvPZTPtLuTHpMl_Za6SyfsX0NHkfxumLre4Q,1866
pyarrow/include/arrow/array/array_base.h,sha256=NsZdt9D3o-uI3IT-WLDfx7THi9HR9RyeOnWVOYKDwv0,10067
pyarrow/include/arrow/array/array_binary.h,sha256=X3WVHp7g98T3Lu9Q-9DytXw50sze1vyiNSamGeGkUtY,9557
pyarrow/include/arrow/array/array_decimal.h,sha256=1qjmbST9xnxPLs4PIiSQPZWkn-a7USGqGU85XbK-mvs,2209
pyarrow/include/arrow/array/array_dict.h,sha256=H3byswFfLK8XxYK7qyBrbmwNRhCXl_uI7o8KGAYS6W4,7688
pyarrow/include/arrow/array/array_nested.h,sha256=gCZpT7N7bYnTIeWYBBPaHcZ7lBcNISYPL88818a3PDU,24472
pyarrow/include/arrow/array/array_primitive.h,sha256=kgSpFomUzCtto44RzWpderbmZJyNfLJf_IVajemw-PI,7702
pyarrow/include/arrow/array/builder_adaptive.h,sha256=bQCfDA4NTImgvGwiBYwE79WaM-3UMazNBlrz72kDN9Q,6815
pyarrow/include/arrow/array/builder_base.h,sha256=3agcNoNyW2WHVdwDC50KHxCVnYXGRNe3QjcJZGeeKD0,13558
pyarrow/include/arrow/array/builder_binary.h,sha256=InVob_76ePxIEhRQdMqQfh6DDce8YYBMC4s4v3bfWp8,24133
pyarrow/include/arrow/array/builder_decimal.h,sha256=Vlo2PGDzshJxYlXMV9xQggJ2wfQw6fiPE7z5hamaIXM,3097
pyarrow/include/arrow/array/builder_dict.h,sha256=yuKHtoyreqTbJbGl0fFPxx5LwD8SLIJ-a3MaACUqjnk,27964
pyarrow/include/arrow/array/builder_nested.h,sha256=muOK_yCvbt7bx6V3EtwOk3tb9Ug5WFC1BWoJwZk-YH8,21173
pyarrow/include/arrow/array/builder_primitive.h,sha256=VkmKJ2UOd3t168XNsXolqkRcn9gIBFHy2OvYZrN7smI,20659
pyarrow/include/arrow/array/builder_time.h,sha256=t7VF68PEsSq8eF4NZAntnmw0lt7nPhS_bPMuA9ibphU,2240
pyarrow/include/arrow/array/builder_union.h,sha256=8mSGeiJp9pwihU5c9LMwgPSescDmnFsHBJreGxULlew,10011
pyarrow/include/arrow/array/concatenate.h,sha256=h1ARgmXdwWv7yQ1Zuf5ARTlYJ8UZeo1G70VuE00XVSo,1394
pyarrow/include/arrow/array/data.h,sha256=3rXDUC_pfxXiFslWQ4Bh226_z2YOCH9NB3fG9pwDg-I,14184
pyarrow/include/arrow/array/diff.h,sha256=vGHW50FpxoMfYMVRvzHcxx3tw-02letyoXkR5yoxgr4,3420
pyarrow/include/arrow/array/util.h,sha256=a_1pWltbXXhA6YQ9ygYc8GsVeWE6zbCMwXocteBRogs,3565
pyarrow/include/arrow/array/validate.h,sha256=9O3KehNl2kWHQwLz6A7izxqW2McmpVcmOE2nz1glSBY,1766
pyarrow/include/arrow/buffer.h,sha256=Z6nKOiVZJqpG0_PJl6mVxB1bwGEU2EI2lTraI8mkFb8,20350
pyarrow/include/arrow/buffer_builder.h,sha256=0pqQ-55bfy0DsSG5Ap9NBS13bMI1ZEwNS9RmyysR_lw,16894
pyarrow/include/arrow/builder.h,sha256=uJA8q2Y-qpZI4gGPR1JBL2qMzjPtF69PBpw5A6nTpm8,1513
pyarrow/include/arrow/c/abi.h,sha256=TDNo0MeaI6dV91vM5BUHop1pDrdSV4DYpM2wUctiaTw,3451
pyarrow/include/arrow/c/bridge.h,sha256=mMmbYvlPh9s7cZqLEDNJ_K_PEGVpPZhJdJY5Eg3BjzE,8084
pyarrow/include/arrow/c/helpers.h,sha256=bo2VGkUxMdktt02nXCEKIoSJmh5LABpSVt35zR8A7qk,3970
pyarrow/include/arrow/chunk_resolver.h,sha256=l_d74TJsNx_KpVO8nMCaU-NHMvveRIvSOH2HYi1TU5Q,3622
pyarrow/include/arrow/chunked_array.h,sha256=KDDU1xTRhRt8UFyt5mKzqLYLEfvd9Gvuye3KZ0iylgg,10467
pyarrow/include/arrow/compare.h,sha256=4c1ulUqzm4gqkh8Rz1i7Iydgg3_vZmxSvfiIg1pvj24,5700
pyarrow/include/arrow/compute/api.h,sha256=B02Tb4BGFX6lq2ZQjinwy373hXM6EHIYXy9GpFFcqhg,2304
pyarrow/include/arrow/compute/api_aggregate.h,sha256=JaiRZd2ZgCu-ppUryQI3ihe1X9axh3ah4X7Nua4rJ4s,15452
pyarrow/include/arrow/compute/api_scalar.h,sha256=UvhkKaHxiLUg1MKrFexdxg7NbIzwNrvx6vr88bZicz0,64414
pyarrow/include/arrow/compute/api_vector.h,sha256=45rX2dFwhoZiQAMNaWPb-2QaYMufYjCIcYaF9L_a4kI,24515
pyarrow/include/arrow/compute/cast.h,sha256=kH2sj6QW5CQM6ge3r7JdepfoOxV1Ai26cA2yzy2OikA,3984
pyarrow/include/arrow/compute/exec.h,sha256=SJyvmv5Du-t17NsGQXsFr57tjztEL0hedcZGCEYVbSw,16240
pyarrow/include/arrow/compute/exec/accumulation_queue.h,sha256=j3FeFDa8XuBFOr967SYl6PPGKUrb7E2ZuP-Zhvj4PTA,1895
pyarrow/include/arrow/compute/exec/aggregate.h,sha256=VwJeCfHfiByEWztCCAJgKV0tnVaZkbLLsBHl45bmP6E,2388
pyarrow/include/arrow/compute/exec/benchmark_util.h,sha256=XuTfnv85x-Xy54QebbbWw99KgHj2pwzLb3WZQKPGQLE,1960
pyarrow/include/arrow/compute/exec/bloom_filter.h,sha256=OHXs-6lgFJJ0gDpo23dNxeXsuesflBTkd268olg14fo,12286
pyarrow/include/arrow/compute/exec/exec_plan.h,sha256=Fw2UXLJvAD80eRs2fyFBlCcdmIVx65QyKL2ZWhooZ5g,22398
pyarrow/include/arrow/compute/exec/expression.h,sha256=ap8CtPZpMnRFuMI3MySECxxo92wNiZ1cbVHiRoH_urg,10774
pyarrow/include/arrow/compute/exec/hash_join.h,sha256=f_xJaKph2iI9oteKO5F37jfwGrhhDjS_m4-PniTMDMY,3072
pyarrow/include/arrow/compute/exec/hash_join_dict.h,sha256=Doh83NUcaj1OR0v0l74lxy5w3wDalIXaFcnNBUvkRAs,15627
pyarrow/include/arrow/compute/exec/hash_join_node.h,sha256=gZ8ZSHcBFUfj3sVBGvEHvRq-fAlyyI5YKKnps-v03jk,4428
pyarrow/include/arrow/compute/exec/key_hash.h,sha256=YAx9CbPS-hsZ95096Hl4uYRD0KTMykZrKPJL6N-mmbU,11751
pyarrow/include/arrow/compute/exec/key_map.h,sha256=C2a9QkpP49864WTuXlm2zG2DpcMrWYYsrCks2yLCsKk,12498
pyarrow/include/arrow/compute/exec/map_node.h,sha256=b7cwbrwujoJnQuyrWQdzKq7_XNbsYCHT5CvEber_bJA,2595
pyarrow/include/arrow/compute/exec/options.h,sha256=uZZCB34pYYCg2uz_-JcIpQFLnrsFN1mtjm0iUmEBSzE,18738
pyarrow/include/arrow/compute/exec/order_by_impl.h,sha256=vKNCjVNPSQjKVzQN_lid9Jf8SgWWuxB8PlNNGWoY_4w,1725
pyarrow/include/arrow/compute/exec/partition_util.h,sha256=VZVufbZQr4-yBeUWlvA_WvJDs8g2Kztmn3O8yb_n1Q0,7607
pyarrow/include/arrow/compute/exec/schema_util.h,sha256=dc_TFBVEla-5AFNfUZ-DKLT8DA_6ySGxVEy6C3HeqXg,8282
pyarrow/include/arrow/compute/exec/swiss_join.h,sha256=19__cyTBa5O-gHc5jO32RsFWxV5Y5xVt3dCTCKrjUzI,31232
pyarrow/include/arrow/compute/exec/task_util.h,sha256=c94OT4det9Heo9D6_W80vgS_FqxE1dm5HtqgNjhlLUk,3737
pyarrow/include/arrow/compute/exec/test_util.h,sha256=cjN3H-AAosZqgX9OTOg3OJE-NbVkYKZcg0PzepO8seg,6666
pyarrow/include/arrow/compute/exec/tpch_node.h,sha256=MzFiT9eo3SDdowjtPvDbX6l0mOyNHx68kgwVQEIbCf0,2699
pyarrow/include/arrow/compute/exec/util.h,sha256=UrlW3sfnrOgLg-QDeBOBWInZhJKpWZPNUlzq1pYOYUM,16516
pyarrow/include/arrow/compute/function.h,sha256=Mb4OJ4LTa88z6qjxAY0PCr9cpGsdQXXRE__FbRr62q8,16738
pyarrow/include/arrow/compute/kernel.h,sha256=iiLHM7QVw_squ9DbKvwJl9RB-2UMhjwP4or3nWGJ1PU,29679
pyarrow/include/arrow/compute/light_array.h,sha256=dZMAWAXTfiaNNNfP1zmsQKJ5J4cBwBNKioGr_Q07-YI,17893
pyarrow/include/arrow/compute/registry.h,sha256=CEtYyvpo3ht44QS57wyxOPS06UAa2mUkXXLVDatkFG8,4913
pyarrow/include/arrow/compute/row/grouper.h,sha256=pFG9LNbTnU0fQlC9x3WeEXWLTYiYW05pAY2t8x4wrhA,3650
pyarrow/include/arrow/compute/type_fwd.h,sha256=gz_q5sh9KEmM_Kxb4nJwb0hpMO7cD1URvUPR6m_eOlQ,1454
pyarrow/include/arrow/config.h,sha256=_-TrLaku5IaDU8KR1K3Y1gmLJKhHv6teKgZHIFC9nEk,3142
pyarrow/include/arrow/csv/api.h,sha256=J-6WfEIWE_oH_WuOS2IJSZAHgKcQuV3xTdNHXneWYb8,1080
pyarrow/include/arrow/csv/chunker.h,sha256=KYhQi-hLLmFIhC3BRfZRbIOpZMsTbnWHS08jy4tcCl8,1207
pyarrow/include/arrow/csv/column_builder.h,sha256=tvZJ71f-fylYtrEmkWwIxgb_s2WLiUnPwyBpBLCHdjU,2968
pyarrow/include/arrow/csv/column_decoder.h,sha256=1Mfwma0yKt5cYVQ-jb4kaHk6VzrnyrI5Pg397R-Mm3E,2422
pyarrow/include/arrow/csv/converter.h,sha256=eBnvOyrqWZ7bexpZc35jBFouaVFFn0NyaAM0P0chcfQ,2871
pyarrow/include/arrow/csv/invalid_row.h,sha256=QI1O1M00RItEnFeMYTvJCUOQ88n-GyO0IantTDu73T4,1944
pyarrow/include/arrow/csv/options.h,sha256=xmUJXmLQVePc7alEXt1vaWWVEU2HpdDOr1SyzvqrgAk,8200
pyarrow/include/arrow/csv/parser.h,sha256=YQAIOaTOlJDqRRZCGknkP78mdTok9ahTKfRXvHV2myA,8813
pyarrow/include/arrow/csv/reader.h,sha256=rrioKf6uFjhHtSzhDnGXD3u4KTAeRS1zuEspGAOX7AE,5382
pyarrow/include/arrow/csv/test_common.h,sha256=ba0-NCml3U4cp0iwBgvEJE7FzECdd67rvWcOvCQT9Rs,2009
pyarrow/include/arrow/csv/type_fwd.h,sha256=udWl775X1Yx5QBgfmXluDDmgI2NStrjmur91H8FNvg8,1012
pyarrow/include/arrow/csv/writer.h,sha256=R-2XDp0AwZcfPt9E0MyaM4O5Kt8w-Xoi17g6-jehS9c,3614
pyarrow/include/arrow/dataset/api.h,sha256=d7skhfQofAt11rkDaieYrZO_HpM1CljUKG2kE3OzMdE,1293
pyarrow/include/arrow/dataset/dataset.h,sha256=bKfYaAzezLURj56mOv3N_p1QHQJvb9OcKrbjtuEZQzA,19136
pyarrow/include/arrow/dataset/dataset_writer.h,sha256=VAgkggC3LuvIDbG0jC1ctohEGTV21nlwdmvnscWy3LE,4422
pyarrow/include/arrow/dataset/discovery.h,sha256=2o4gC45dFWxwNAg2MOfAlrbnJ_izzz9FigGbbc11PGA,11275
pyarrow/include/arrow/dataset/file_base.h,sha256=OQFpNv1XdzA6puARX0Wfh2tnXGDsvH1JpndJfhy-TJ0,18179
pyarrow/include/arrow/dataset/file_csv.h,sha256=DM9YAe4hCqdGHJBt_SIwnfNqXvIM1Yul0QXQrCTaE3k,4520
pyarrow/include/arrow/dataset/file_ipc.h,sha256=vOESBHgxtE1TLEu2w2pYijLRRrBNSLkgqpTRmso2keM,4184
pyarrow/include/arrow/dataset/file_orc.h,sha256=ttvM7FxZqunlHwh0uRRFrcooRLPigH_t5yo0eme3QZk,2505
pyarrow/include/arrow/dataset/file_parquet.h,sha256=Inl3DS_-UVXvuKtrgvyaE2636ZR6zt_DbgeHETmRsmg,15529
pyarrow/include/arrow/dataset/partition.h,sha256=AOqL1v6AweZh1BTbpfOQ_Bp7_v_wbyjekB1vQ1J36a4,17139
pyarrow/include/arrow/dataset/pch.h,sha256=3vmHGXlgxa9tBmj1KBA3bJxV1dy_Vp_32vQDeivp5iM,1221
pyarrow/include/arrow/dataset/plan.h,sha256=e3HxDV55B9606UzmQfJOchsZYvxNjjSRW1sSeqRdA38,1214
pyarrow/include/arrow/dataset/projector.h,sha256=Gl7qon2MGuQdAXFC_VL7lDmekOoaIYpkDbqvhHaOmtg,1167
pyarrow/include/arrow/dataset/scanner.h,sha256=Qi9TA8Kte-5VhtBOKebsUW4oktvQHnPymb-A6HH-yPo,24015
pyarrow/include/arrow/dataset/test_util.h,sha256=kru5WU7mPpr4bsXOBgXssZX65YGtiiY2lJ63Fw7JeAo,60408
pyarrow/include/arrow/dataset/type_fwd.h,sha256=_suXzy1pJjqNr4zCtTHBMWKDoXZeowp1I1-XfZWi0Nw,3173
pyarrow/include/arrow/dataset/visibility.h,sha256=R-pIFvth6L8hVVOgZAi35xKZFmjnnLmpa-ikB8nEmug,1578
pyarrow/include/arrow/datum.h,sha256=bG753RqRKtxIUqazl0bCNnP2W6eGwiTTmjt4lzR7-u8,7568
pyarrow/include/arrow/device.h,sha256=Ka-JMdSlCQ3qU2Z6PpCEDOnlO9OSt4fPp9INZ_ZSqME,9850
pyarrow/include/arrow/engine/substrait/api.h,sha256=N6ztbqu9QYKbzgFRJedYqIHd-qf0-KPQhjaXWrl39Nc,1014
pyarrow/include/arrow/engine/substrait/extension_set.h,sha256=lQNLoVBg-Dz4rZujplu4sGsJNFFfXSVHkF0GrfbmZZk,20356
pyarrow/include/arrow/engine/substrait/extension_types.h,sha256=L0AJxbGoF9fGcfXCmbwH-dn0Sxt5Ibpw_M-ccCcusq8,2793
pyarrow/include/arrow/engine/substrait/options.h,sha256=uU3p-pdzPrcUADE0KEU1zLzaLUGSMsyspuDhbcEfv0A,3662
pyarrow/include/arrow/engine/substrait/pch.h,sha256=SCfsZcIE1gMn4HDG0mcisMTc6AY707D_MurvzJNYnKg,1117
pyarrow/include/arrow/engine/substrait/serde.h,sha256=2jVAXNY2_BovZOo9wKAKyFdg_IrC933kD8FABKpX61A,14486
pyarrow/include/arrow/engine/substrait/test_plan_builder.h,sha256=djnnpNv2JAvceL4LkDkYlmKU5QEAcESYHicy0X6RFb0,2990
pyarrow/include/arrow/engine/substrait/type_fwd.h,sha256=LOMn522HgejuNWUlD-Ect76nMxNVAheGvnFxn6GPf_4,1035
pyarrow/include/arrow/engine/substrait/util.h,sha256=BrbI71i7ijnBU9TFUl1ZktjgNFyoy4EBZNIZG4UacKQ,2046
pyarrow/include/arrow/engine/substrait/visibility.h,sha256=VYzGn6u_t9TxW0x3zhUYfXDhbuHlwlsXzmhjYK4gJhY,1735
pyarrow/include/arrow/extension_type.h,sha256=d4l8C5p49C-ajci_ckp46V3fR_4C3grz157__shMaXo,6580
pyarrow/include/arrow/filesystem/api.h,sha256=Ke9o88OUWfy0W05fuBFpaU_wrF1VmIti0xElkAqQpbc,1323
pyarrow/include/arrow/filesystem/filesystem.h,sha256=VxaHNd930FHgC7y95vBXY2aldvqL8R4YG7FwvXzwSN0,22095
pyarrow/include/arrow/filesystem/gcsfs.h,sha256=C9NwVagXOrELnAh8AkGNeBS8hbi3xZbptidM3NzqjJk,10348
pyarrow/include/arrow/filesystem/hdfs.h,sha256=PR78L6Pj2GrRKjIgVYyd7Pa5TdjDgpiyg0KsRCeblWk,4073
pyarrow/include/arrow/filesystem/localfs.h,sha256=GsVuz4lqi_JBnPlTVcfmcgdfmbZxZMQYMqPXoRZxZ0Q,5018
pyarrow/include/arrow/filesystem/mockfs.h,sha256=uaB70NwmVqRU1svHn2XIxt6ZyWGT9FncAwHi853Ki6I,4818
pyarrow/include/arrow/filesystem/path_util.h,sha256=LB2NWz2ZbQVDapKJSURH-RG-_M-ZS1ZrPYqtZV3tHsg,4663
pyarrow/include/arrow/filesystem/s3_test_util.h,sha256=hKQRFg4yKtNPexQBHYMSbPAeOGSf_cHAHJlYv_OFSMw,2385
pyarrow/include/arrow/filesystem/s3fs.h,sha256=BpDt-NVc3sYFbyqfVXZZakLYUoTWkT-TZKBVDSwFM6c,13386
pyarrow/include/arrow/filesystem/test_util.h,sha256=00NDcbp0s66Z5_9hcXPWPleD9KOfCq3hN8MzNnnLL2Q,11018
pyarrow/include/arrow/filesystem/type_fwd.h,sha256=VzLpckS_LAaLXN0J4XwSx2EDGk9x9QQzU2APyTmYfHg,1491
pyarrow/include/arrow/flight/api.h,sha256=7Pptj5tCitEBs4kD6Cuz2MOCp6Qze2ojyZNjZ7uSO6Y,1248
pyarrow/include/arrow/flight/client.h,sha256=oQlit-ukliuh8bEui3WBA_5E2uVv9L6rbOeJbuESWp8,18996
pyarrow/include/arrow/flight/client_auth.h,sha256=S8w0hGHthx5trhh4Hy2IIUZhlMv-5hadI9vZr61caDw,2278
pyarrow/include/arrow/flight/client_cookie_middleware.h,sha256=1fsK1K4nsZya2Lcu7ObLFmy8MySWAndeOLqbtELH33w,1237
pyarrow/include/arrow/flight/client_middleware.h,sha256=v1WbZyiSxPzEThT7HsjMHJ96r1VqWWbs0_-s6eINIKQ,2781
pyarrow/include/arrow/flight/client_tracing_middleware.h,sha256=htiyzC8DGhxUxP4mT2BtC2Z7YbyKIEOPMshLh98W1MA,1251
pyarrow/include/arrow/flight/middleware.h,sha256=hglVb2gIU4jAoSoONAMi7qMA2SJJ52258-MxAjYj0nw,2535
pyarrow/include/arrow/flight/pch.h,sha256=Dd_7inDS5gHboIRsPdHm-CdyiyGfyaJWs4A8QIPZlG4,1218
pyarrow/include/arrow/flight/platform.h,sha256=rPiik2bUsHHIQET6loV2jyToNOnznD7U9mk__cDEqXs,1238
pyarrow/include/arrow/flight/server.h,sha256=U0DEBJCBDdIvxNI0nd1x0AKXx__I4Zfz9C7Rd2B3X-g,12343
pyarrow/include/arrow/flight/server_auth.h,sha256=gLO_YuEGwOmJyKkhnd45SKzA_VwSdVpt9wGnjUYa9IE,3046
pyarrow/include/arrow/flight/server_middleware.h,sha256=0w_wRTiBzgZzc5SMLW-utHN31IshBculaFwYu1nk20Q,3310
pyarrow/include/arrow/flight/server_tracing_middleware.h,sha256=4Ovp-oUJe9MT5bM_mlN9MoYLXB_fySwUTy7C6kw9k_Q,2254
pyarrow/include/arrow/flight/test_definitions.h,sha256=Fw07GYz-Pl9Jao5ZRI6InfFKkNJNrC03Fuf3Po5I7Rk,12034
pyarrow/include/arrow/flight/test_util.h,sha256=1obOp_aK24489DZ5cWb--gL4SRePbKpLM61AqxtMI28,9410
pyarrow/include/arrow/flight/transport.h,sha256=eNPc6nYQq9MaKIaVcWUI2jdf8GHJmsUmUp2vcahLZ7E,11094
pyarrow/include/arrow/flight/transport_server.h,sha256=8esv3jl-gvDFhFeYOHC0-cL6YDjTt094Tbk987Gv3Wg,5405
pyarrow/include/arrow/flight/type_fwd.h,sha256=jDp41N4YyA6u9oPiaRVxgbgZeLRHi2U4s6N2Q7uUS-A,1740
pyarrow/include/arrow/flight/types.h,sha256=bZGNXpW48I3AYKqnXd7u-ecWpzSdp4CKMJPiDwF6I3g,24833
pyarrow/include/arrow/flight/visibility.h,sha256=YVJnD0qyQloY0Oy4oxWCaOtfdHBTcLHabDMMrrZLEJw,1586
pyarrow/include/arrow/io/api.h,sha256=Dv-t-VQoHXkWlCaBqhV4JNRpbr5VPPSkidR3OuX64fQ,1021
pyarrow/include/arrow/io/buffered.h,sha256=F_RN4E9ctK86Jq6feBs2oDrR2C0Wio-K_CqIjBZOzts,6012
pyarrow/include/arrow/io/caching.h,sha256=8vYGhWd7eRdA2enJ2-ktKC_jcYVZKlqJKP9_YJmKwAw,6227
pyarrow/include/arrow/io/compressed.h,sha256=Qtx9EzXhGZoeEAllQk1kbFCjjNymZlr9AxBngVQsvaY,3652
pyarrow/include/arrow/io/concurrency.h,sha256=rDbsAE2oDU5I9-QOMRNL57h0MLqVINGS_Oll196m8ZQ,8223
pyarrow/include/arrow/io/file.h,sha256=mud1ILk2zK3C_jap1m0X3SWa2t_fJKi_PIDIYIkWQnI,7846
pyarrow/include/arrow/io/hdfs.h,sha256=_aRZhApwTn9FEUzDGM0eAjt358DIQhxJjHDtIkv3Adc,8843
pyarrow/include/arrow/io/interfaces.h,sha256=Goj9dO63VbYi3oqO4BTbY0lgcI9xKO5TLfKu9J3OLb8,12930
pyarrow/include/arrow/io/memory.h,sha256=qJapXmeVC3NEH53RnGSBjXxwEmFSLOVRljVkeJPLDcA,6535
pyarrow/include/arrow/io/mman.h,sha256=pQ1W1RONae-hoWai-h3JSYrWodOS03h1JWEPDlEqYGM,4278
pyarrow/include/arrow/io/slow.h,sha256=E0RbN0tQQRJRIUgM--NznPmI6fLH0bxnWct1jsn370o,4060
pyarrow/include/arrow/io/stdio.h,sha256=FhklhNBsYt0fZZKBmgBfMJ_y_QPcEBkD62KQqedy7Qk,2177
pyarrow/include/arrow/io/test_common.h,sha256=3wG6--ZjPWqQFL7gI8nq7Uta4cUOeDn5hQVReXTZuTQ,1799
pyarrow/include/arrow/io/transform.h,sha256=99VKVlOO8M4HF5121bJ5iAyi0eUMqg0ndafvZ6F4y_M,1950
pyarrow/include/arrow/io/type_fwd.h,sha256=ebFvHA60xc-jpcNt7AzadAzwO-LWmFXMx6AMZMgtcIM,2392
pyarrow/include/arrow/ipc/api.h,sha256=XX2g6bMVdnN9pup7-ad7YI876hxfEOPk_5KWO_TM0w4,1032
pyarrow/include/arrow/ipc/dictionary.h,sha256=RWQcycPKBl5aREIPi90Yd8jPqLcw6ioztlHAHpj1W0c,6281
pyarrow/include/arrow/ipc/feather.h,sha256=LcTc9nldeLieMbtMidRgdvM9X25X4hEaVjZOHAYeofI,5068
pyarrow/include/arrow/ipc/json_simple.h,sha256=GHLMKo5y6d25ysYJEeEKInguimCyl_md7HFZ3Yl-x7I,2526
pyarrow/include/arrow/ipc/message.h,sha256=sAAyRIrgsRX3L8WMoQ2YqBg_mkBaUazvPjaR3nKYMnE,20576
pyarrow/include/arrow/ipc/options.h,sha256=AypcZmqWypfZ9L8IWjmYhoX-oFhzyum77gr_yE5zE-Y,6337
pyarrow/include/arrow/ipc/reader.h,sha256=ijM5M4Ub0ld-SP5-R0d_THj5E0bx0w4_NnBuypzdyrg,21624
pyarrow/include/arrow/ipc/test_common.h,sha256=Iw13fQSI70lG96KICb8P_hPK6DaurzRqjDNfNFrwwT0,6074
pyarrow/include/arrow/ipc/type_fwd.h,sha256=73HA_hADPhvXj3NiLOs0s3WXO883VgfGiirDvoNcNpA,1453
pyarrow/include/arrow/ipc/util.h,sha256=xJ1KaQe_wnYd9zfzVgJlLgGTVQQqxvb0xnZYutiHkfg,1455
pyarrow/include/arrow/ipc/writer.h,sha256=D5RhR9jDx5XbREufTmjJfSncW18VwNWtFPvDnp1ZGfk,19399
pyarrow/include/arrow/json/api.h,sha256=QD-9FK6Ad10h03vRS5PvQWBX3jn1TQ2pTBhLv3kSRm8,900
pyarrow/include/arrow/json/chunked_builder.h,sha256=F9WcNFIXy_q9_Uc6-c37RvGgDFVPGo6bhh_OYIyPU68,2433
pyarrow/include/arrow/json/chunker.h,sha256=HPPfHXfhjAj8rtU-RmseUhEqzKtwP64UQdiikMw4jAg,1154
pyarrow/include/arrow/json/converter.h,sha256=0Iwxxsr0ACxdQlpmFujSwVeF43N3RhZ_oSrea9YV8M0,3228
pyarrow/include/arrow/json/object_parser.h,sha256=_Disb6bNNZKBdgWgK6RUa_6bb0Oj3OdtEcSFP4gcWgo,1494
pyarrow/include/arrow/json/object_writer.h,sha256=PexnKjvOBOGRsPxuzQNJXTkNneUhOZf74rh70vv1Gsg,1458
pyarrow/include/arrow/json/options.h,sha256=pQRNifTwZ63y6pOBumz6Z8IWHXDUAUQKBPS991Kou7U,2301
pyarrow/include/arrow/json/parser.h,sha256=G26cnaXKNVfDLhvznU7cwjDbTASxhli_PHqjYgxYIRI,3539
pyarrow/include/arrow/json/rapidjson_defs.h,sha256=Yze4FHiC1DXZsWFSM5D_IPCQhxALR5ELSmE7DR_CRwY,1511
pyarrow/include/arrow/json/reader.h,sha256=X9WsUVklrZNvJ0NSA3HS602wK4LkRH65hVk_gLivfQU,2135
pyarrow/include/arrow/json/test_common.h,sha256=v2XLPiVszDfP8vs3CIhbi_ng9Hdt1SCWfmrIhsGacns,9564
pyarrow/include/arrow/json/type_fwd.h,sha256=FWkyYf2wb91GygRRLXTTIFvvoztO1hfxNDq8TtivOWs,968
pyarrow/include/arrow/memory_pool.h,sha256=yN3r3J3tXrolsH_cJQT1kJZLOt0gfm5-CYSwWCDjohk,7915
pyarrow/include/arrow/memory_pool_test.h,sha256=H4p9702NOFEmY0945J9E87ImXtJZGM7NXbNPzDrmT0o,2853
pyarrow/include/arrow/pch.h,sha256=09MmEJKJaxyGuyDLA8Cs2uDcK-6oUa-OFwj9hLnihiU,1316
pyarrow/include/arrow/pretty_print.h,sha256=S69K1F0S97-P6-XqROLN9htRWWkK-BjBKBlaG247t4c,4532
pyarrow/include/arrow/python/api.h,sha256=biQIS5I3je_V8G078KUnuhQfdUp1lYZSkqlrNvm9I_A,1252
pyarrow/include/arrow/python/arrow_to_pandas.h,sha256=K_gBYCLovD5C0WrislmQoFIwVv1LPOpYPCwPDjM2_Z4,4574
pyarrow/include/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/include/arrow/python/common.h,sha256=JaaPMEZUWBbkd-ShpaAaLejpcZ7JC0MKZfhl0q9KKb8,11659
pyarrow/include/arrow/python/csv.h,sha256=R_wZpPdMCNgw1ivJI7W2IKiuQcGxBZWK4RLjPDApkUY,1439
pyarrow/include/arrow/python/datetime.h,sha256=umPCbKpVvH9Ri7qZBzg3R6VfrL0EQVbLRJQwfNaglpQ,7462
pyarrow/include/arrow/python/decimal.h,sha256=BAt3laodbJkc9CsIEN0Qi1l2Ra9Fuznv1wNc5d4r8gw,4854
pyarrow/include/arrow/python/deserialize.h,sha256=LYP0t8DhzevH9IQ7A-xyF391ZdDIMcwY-N-U8k0l1as,3995
pyarrow/include/arrow/python/extension_type.h,sha256=A7kopjTlpX3yI94b2aub-kOnjRq0VaC6ocRuWYJCHC0,3240
pyarrow/include/arrow/python/filesystem.h,sha256=y-UyYdHItSA7Hm0JOlYxqNzN8iNOc8L_tx_iXOfRbhY,5116
pyarrow/include/arrow/python/flight.h,sha256=CV9HoK-GlwD5sCCn2YVByTfbUET0cRghn9GQGpLLlLI,14619
pyarrow/include/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/include/arrow/python/helpers.h,sha256=JMORNQnrVGfpTAegLwyUJpzhROmQB29EyI33c4eQVpg,5600
pyarrow/include/arrow/python/inference.h,sha256=w8eRO1s9YA5L0kBffyPV5SS25ZIuh84t2JKB0C0PT2I,2102
pyarrow/include/arrow/python/init.h,sha256=Dfdy6kMtFHc_NW6EhionR0ZWr-S6XpaICdu1PzTl-E0,974
pyarrow/include/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/include/arrow/python/ipc.h,sha256=8oyMIbSQmMCZSUoy1K_oNGp647U5e2eUcfiVIiZqpaA,1716
pyarrow/include/arrow/python/iterators.h,sha256=7JRvhoQL0V7lOPGxhCk_1j_wUl_Vc9jDIOf5nhU1Yig,7387
pyarrow/include/arrow/python/numpy_convert.h,sha256=PlXV4R8P8fQVVIlbersPECXM-K3F4uIWEUmiEgmTMKU,4900
pyarrow/include/arrow/python/numpy_interop.h,sha256=p-rj6NaZbniXswmkuquLS2L1DsUGq-GsbRXqxzw2sq4,3220
pyarrow/include/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/include/arrow/python/parquet_encryption.h,sha256=KS0h5-2nShhr19UbLmiVEFI_8gkeFnYxZk_COPkNdzM,4187
pyarrow/include/arrow/python/pch.h,sha256=cuGs6XHl1WaiL-RuVk8stRK6hquHo5mjD6z0QsGQ_uo,1153
pyarrow/include/arrow/python/platform.h,sha256=u7SUqHP7XrkMBCvXQT5kk8cBYVgZxoT7l9TqHCe19yM,1296
pyarrow/include/arrow/python/pyarrow.h,sha256=IfKMw4LulYTf2k_FvwVAbYw6Cuf3BEDuo6LdKWivtY0,2608
pyarrow/include/arrow/python/pyarrow_api.h,sha256=cmCIRRBTzAn13xc2OvSneNKyb5DFE1ui2l-yfsHPGg4,20346
pyarrow/include/arrow/python/pyarrow_lib.h,sha256=-frs4Snxe4UaZ0iYIk7NsL10uRXWbV08uy0zVge370Y,4987
pyarrow/include/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/include/arrow/python/python_to_arrow.h,sha256=-LwRqP3EowR32figOboT7L8QkCoqmrajv_PN9WzxGTI,2601
pyarrow/include/arrow/python/serialize.h,sha256=bLDzKvtqPt9fOZ0dVFO8WKGrfOa8vvbTHd-NLbTXPdQ,4540
pyarrow/include/arrow/python/type_traits.h,sha256=bhn_U-tmE9OX4GEJfFqz41Lf3tBwQPfl5wB4X3BHFQs,10443
pyarrow/include/arrow/python/udf.h,sha256=rdUsfJMVwl9qu6LrMMDi7QjFpqfuOwxI3zKgOOKuWwE,2213
pyarrow/include/arrow/python/visibility.h,sha256=4FcBJfEIEsf7s36wakgQL0UK0tFdHDckk3oWrOGF0eA,1378
pyarrow/include/arrow/record_batch.h,sha256=cAFjaWXi0cTni8THlmipAEMAVkyDdtWRGiMLUC8UL8U,12895
pyarrow/include/arrow/result.h,sha256=DqsDkMvd7LNQIDW-nZwjAPOYRxrKQR6UpQ2uvlz57Sc,18230
pyarrow/include/arrow/scalar.h,sha256=nHnHxnEjh3CROCbVNmqkO7WGA73v7UZzPk3laVr9rFY,24147
pyarrow/include/arrow/sparse_tensor.h,sha256=wlyVEvtE_a0ERgsR7OHRt2VqOP2Y6e-tbe2nkqbx1p0,25822
pyarrow/include/arrow/status.h,sha256=TDVC0eZ6K1iei_ifWW2APbOAFbQ5Wc7vJOXef7LNHRw,16637
pyarrow/include/arrow/stl.h,sha256=jQo12sZ3e7sbnuDwRNRh_1kJiZ825Lcfsa54bxtlowU,18626
pyarrow/include/arrow/stl_allocator.h,sha256=gBBd8RBnoFeH1nyhQOUtuzbiJxP-ix9-KCnicAAlw5w,4767
pyarrow/include/arrow/stl_iterator.h,sha256=XSt7wFb2Yy-hoDVebEIX7sCQyTGFChDAVNyy8CkV6mo,10267
pyarrow/include/arrow/table.h,sha256=DNVW0jlK7pkdK7bYDMzPNgGEiZ3-_CKQvZ-kUM_v0I4,12523
pyarrow/include/arrow/table_builder.h,sha256=lGXbNDjB8entK7a71qOaMW88Sd_m4avgeYBdyvd8wco,5482
pyarrow/include/arrow/tensor.h,sha256=AVYwo8T3UT1dkMGlitM7D2RcOFt0DJ5aqfrx7_WRqDk,9159
pyarrow/include/arrow/tensor/converter.h,sha256=BTiYEWpV34uKMP1hgVxdoeyBlh-cqpmVaaBeIc2ARig,2958
pyarrow/include/arrow/testing/async_test_util.h,sha256=miNSGn91JvXE1NojQusPWGTINiJkk5D5xurkPNwyBWk,2341
pyarrow/include/arrow/testing/builder.h,sha256=isZtD42iB8qiTKzyfaeE3RIdLliLR-xETYx7jXJZ4Uo,8885
pyarrow/include/arrow/testing/executor_util.h,sha256=3WdGxQ-F5NfkJVCR0jv7hYG5k0YtdXuOoxACY3bmY7Y,1940
pyarrow/include/arrow/testing/extension_type.h,sha256=CtyvB3joYtTbU3lScjv2slZSrWTY64r_xweV00tqkRQ,6786
pyarrow/include/arrow/testing/future_util.h,sha256=CyeXP1q0nDBKhFD2JpzHFOKhWhLa7ItvQ6OdQgm82Zg,6388
pyarrow/include/arrow/testing/generator.h,sha256=qWrbkdQAlnOBEcs7CCURV5xK-62Bs0yR-WSisWxiZ-M,8598
pyarrow/include/arrow/testing/gtest_compat.h,sha256=bKnz-VP8y7QTV4Rqa3I-hWY-EfBqO8CwPrdN_WgGE7o,1332
pyarrow/include/arrow/testing/gtest_util.h,sha256=sIXVjkZSuAmKM9xXXRyvbMNLD5Rmt3Nj0gmc5Yeq4dQ,23096
pyarrow/include/arrow/testing/json_integration.h,sha256=zBMJpOz2StTdP2iHbbVUtTkahFAjZNEdL6eDQPdHWqs,4333
pyarrow/include/arrow/testing/matchers.h,sha256=uPpJq6uDSEktkveM_RFidINFv7ePmTkfXK_ava7sCVU,17242
pyarrow/include/arrow/testing/pch.h,sha256=CDXIaNX-5nbOaUj6udZil-7sI8UBGDaUk7ytXjRHH5w,1189
pyarrow/include/arrow/testing/random.h,sha256=d191prNM9X52vAR4qi18eUNW90sFd2lW__XwCVtOlW8,22716
pyarrow/include/arrow/testing/uniform_real.h,sha256=8Us2dKOV8LhsB-MawOoCBPc7eLzP35KKWVemK0UZoRE,3054
pyarrow/include/arrow/testing/util.h,sha256=Rl9Pco8w8w1am4-kK-Oz-D8oIGg0GPL0gUSYiwzaKWw,5251
pyarrow/include/arrow/testing/visibility.h,sha256=Rf9HfEuXNziHZQw8CW8IZ5AEDNeVWCArO0PlyHK4bos,1596
pyarrow/include/arrow/type.h,sha256=TUer8Q4dxfi9Mhk6N8FF4Zr7PFjjLvf2BitY23BX4u0,78931
pyarrow/include/arrow/type_fwd.h,sha256=zdaYsoAXtJs1uLT0v3jHs81qIozOdOl-fLTZojW0ioE,19106
pyarrow/include/arrow/type_traits.h,sha256=UJxGAyXpwMQxG_kkNf8gV7YhPskKAtcvdrM-L5V2LjU,45930
pyarrow/include/arrow/util/algorithm.h,sha256=okcbieN_LA2UJ29QIEQ8LcF9EblZvyu4iO9r_QK8deY,1262
pyarrow/include/arrow/util/align_util.h,sha256=dTK_XGpZYmWenYpV4o0jg4QPblPQYmPSgAHaONWasjE,2706
pyarrow/include/arrow/util/aligned_storage.h,sha256=2lBntZb0Ni6VAJQ6GB7Db4Qw_yAH98t9laqC6Ir-Lu8,5132
pyarrow/include/arrow/util/async_generator.h,sha256=Jbq9XwJ1tmY63lpi301GSfjizYA6yyytK4knvty-QY0,78612
pyarrow/include/arrow/util/async_generator_fwd.h,sha256=a2jJSFwcbyEem7skH2_OZUCElg3K0c-pM08WOdvnpag,1808
pyarrow/include/arrow/util/async_util.h,sha256=winz0Ax_NRwppm3n8XYkqILjNvKG2k46eqDUXP16vRM,14848
pyarrow/include/arrow/util/base64.h,sha256=ihRHtpYtb8xH71Zu2goxkm7LKIAnQkzlsSgSUQu7BlE,1130
pyarrow/include/arrow/util/basic_decimal.h,sha256=g1VocGe1o6OG97SnajmYlv_x3MsUwxtni61VhwFIqGI,19172
pyarrow/include/arrow/util/benchmark_util.h,sha256=dTweZyaV3XsJkhhKcCV1yVLHc-wxyywYWjB-gP0vnE8,4746
pyarrow/include/arrow/util/bit_block_counter.h,sha256=8Ny-hQi9vAPVruuYWEbpt5Ts1jfAqGdzS6Ja6O0Xkbk,20733
pyarrow/include/arrow/util/bit_run_reader.h,sha256=xOv8udorWzpetz5KtUaFBSkva-Hps75HLRKnbc_OrbA,17131
pyarrow/include/arrow/util/bit_stream_utils.h,sha256=ZCT5Cf_YFzBEsxWipizlXdgzWltAd9IdG9Zmvx3gW4M,18398
pyarrow/include/arrow/util/bit_util.h,sha256=7uinGearNQzhblOCOUCqffoSTISoMU-toXbPnIu5WxY,12351
pyarrow/include/arrow/util/bitmap.h,sha256=P0F-MHWQu7QPwvuOE7v5mMvHxW51lbILKgXAhzzKB8k,18009
pyarrow/include/arrow/util/bitmap_builders.h,sha256=lkBf2Ej19bmQxeFklmUV7Z4j7-F7JmRLfGC0JiY-uFE,1606
pyarrow/include/arrow/util/bitmap_generate.h,sha256=VGBo6Gbe3lVcR2gVyRyQ3K99gMNwOZ0FgZKmxzwsEzI,3678
pyarrow/include/arrow/util/bitmap_ops.h,sha256=40TmVx6C1ETGX8OBcETQTno46XyA0_rBAJXWBzzIcSQ,10994
pyarrow/include/arrow/util/bitmap_reader.h,sha256=oAX_gnVCW9dJtlxy2pzFy3bC_asjy7_DQ2A4XwC1Xfw,8627
pyarrow/include/arrow/util/bitmap_visit.h,sha256=PU07JCo3_618ZWJMxiTIZW6dfETceXgbjfU1YeEfyYE,3558
pyarrow/include/arrow/util/bitmap_writer.h,sha256=aoaggO8m5T_bFiRqaEsJDkHkVa5KRED1iNbJf2w0U4Y,9668
pyarrow/include/arrow/util/bitset_stack.h,sha256=10k45pz7ZzWrJwTuGqcLztVLg71wfFoabykodNsnGBw,2865
pyarrow/include/arrow/util/bpacking.h,sha256=e4TwSlBz3V6YjsWGUSMwi0por6GtEN07t7LtjuSopeU,1209
pyarrow/include/arrow/util/bpacking64_default.h,sha256=Ov2SA9sQmohXSHw0Rb_zytyVQ54TJK61V_smIZMdkac,202632
pyarrow/include/arrow/util/bpacking_avx2.h,sha256=WKb37t4ch1eOkeNGySTiKAG1lfCN8Cvtnjy8aFHt9Jg,1037
pyarrow/include/arrow/util/bpacking_avx512.h,sha256=H3iqF8SNMT4szyzvv6gSwdXMmNitTqN76tDELLYG9vo,1039
pyarrow/include/arrow/util/bpacking_default.h,sha256=OjfkZlZhuOYn0rXQmABEFctBSK6TQDDATrUUAktYBGE,108011
pyarrow/include/arrow/util/bpacking_neon.h,sha256=aukHePChI9vXwUjSCXZ7YMjQmHh7xCtxm4iHKlVs7zU,1037
pyarrow/include/arrow/util/bpacking_simd128_generated.h,sha256=f3748O1bJS1OqMXntosAe9C46AaWTxujW4I5hsczGUI,100569
pyarrow/include/arrow/util/bpacking_simd256_generated.h,sha256=1gS18elwcinsYum85DWLZwnslYbn4yAq5lPAyRlXhaY,78719
pyarrow/include/arrow/util/bpacking_simd512_generated.h,sha256=Y9mWhFzun9v6hhekdjmvas2ItcJyHfRf1fhL-mXZhkA,67888
pyarrow/include/arrow/util/byte_size.h,sha256=Z9HxkPG4DAMSJ5bxpglqUP3UP-vnMViRb9KgTrOhh_s,4085
pyarrow/include/arrow/util/byte_stream_split.h,sha256=fIaoNSdxgsamsmA63z3apb5wkcCIHJH56cWHBeF4SW4,29410
pyarrow/include/arrow/util/bytes_view.h,sha256=rIkA2Og7ooCO5HUNObgWyYa3RjPtC0uNhETPWQZwoxc,1020
pyarrow/include/arrow/util/cancel.h,sha256=1ZAcnVSNxKlf1gqFpr7FVG0xwC1uko1FPcY_EvFzxAQ,3013
pyarrow/include/arrow/util/checked_cast.h,sha256=0XkAxq3kuBxU_pMZ_r6rmgovb5zoFHhq9nZNuWKkgVQ,2137
pyarrow/include/arrow/util/compare.h,sha256=3FdpAeX1gAKkcPc8NApQR8Ov-iMG6k3Vwe8AEWu7-bw,2043
pyarrow/include/arrow/util/compression.h,sha256=2qDF-FlSvSIC3ardpGGE7xcDDUWy3dr6H0J3py9vDhQ,7569
pyarrow/include/arrow/util/concurrent_map.h,sha256=hmRw2luX9OVbYwIyMXc3fEgof4An4owLnWrAsKgXKpM,1843
pyarrow/include/arrow/util/config.h,sha256=faqHFRWZ4PUUk78hd9226GgkianPvB9aacqbYqHCCbk,2090
pyarrow/include/arrow/util/converter.h,sha256=9Wl7M4FSGKwj_jZnVDELWwkkNcd8rJmlsAeOwZF3W1Q,15048
pyarrow/include/arrow/util/counting_semaphore.h,sha256=pjZ6Sq7cAbmTVwIeSoCS6je4ru00KKXHzhtOUpxLItE,2311
pyarrow/include/arrow/util/cpu_info.h,sha256=Cg7Alr-NR4E89JZO9KlAo5bH60gVUcvpqBOKZ_yUF3Y,4078
pyarrow/include/arrow/util/debug.h,sha256=Y8UN-qs-hijFAWr-LKoNq1dpI2ol-krDuy1xTCVYgb4,1000
pyarrow/include/arrow/util/decimal.h,sha256=H7HtYL8a-ZIa7e1Lhq22BPJRlonkrd5s68kwHhcc-1k,12066
pyarrow/include/arrow/util/delimiting.h,sha256=ZFc1v-DOeBEBfWYcN4-cnxvQMToxeGJeJZ63Z0nrWgc,7498
pyarrow/include/arrow/util/dispatch.h,sha256=_-IP_k9Cdw6p58XAiuxjXE9G9-na6vb9HoYXBP-WRl4,3350
pyarrow/include/arrow/util/double_conversion.h,sha256=0Qyg27rEn3aUeKAfKmyqsaAFKf-Xmt24rtU3d3Hye9I,1227
pyarrow/include/arrow/util/endian.h,sha256=GK-UdLhm9ogqWxRZAk2OuyQEwz2k-mBIukgGOdgQ6vU,8358
pyarrow/include/arrow/util/formatting.h,sha256=H0ddKpOeO6Jpn_-eFVjZCkLsBXFmLynKWvHk-j-Yryo,21042
pyarrow/include/arrow/util/functional.h,sha256=vVBnLo4584uiCYaRQSA-2BzzsNSsyjWbEdgsF6L0a8c,5772
pyarrow/include/arrow/util/future.h,sha256=poy3zvRgWsvX31odz8O7OJaxpHUgEdXmqz1s3Km9WcE,33191
pyarrow/include/arrow/util/hash_util.h,sha256=qMwNgyct19kOpUX10ULZIZcEhGi1_nYymcardJP1h10,1980
pyarrow/include/arrow/util/hashing.h,sha256=db55lTLBKRQ9ksZ-QMzjJKMKB6MaPh5ZIY5sEBuboNQ,33016
pyarrow/include/arrow/util/int_util.h,sha256=K5kRewMow9PCIFcE9MkXR1178UWcsKYoNqVOzEzBds0,4995
pyarrow/include/arrow/util/int_util_overflow.h,sha256=jYzJYZIjyFCVLseIKq4wAR5Sl7jqEXOQa5nDWEEp4Zg,4983
pyarrow/include/arrow/util/io_util.h,sha256=kNzLWWrMbxSG8-5vSsLnDe9pFuePWxegC_OZZTz5S2c,12621
pyarrow/include/arrow/util/iterator.h,sha256=bs6SV51G053EW5An_jKj3_E0FUCFOmkhSUgb7Xl-J4E,18669
pyarrow/include/arrow/util/key_value_metadata.h,sha256=_vEFWw4ZqAD4H46LbKKPWj91JTKV5uM57OA8iHonNF0,3687
pyarrow/include/arrow/util/launder.h,sha256=SSTWzD4H-Oy0TExl13n4sWqHiRLlKtBxT22r7JZZ7ZI,1081
pyarrow/include/arrow/util/logging.h,sha256=0bIm2sPyQN2pYvOaxbtuNE2cJJris26lnkMl7eaqQpk,9359
pyarrow/include/arrow/util/macros.h,sha256=IRLIfyCH15iPSfjWgeuhdZFWv0-M5FrLlZimS6B9-MU,6433
pyarrow/include/arrow/util/map.h,sha256=dRoAZDkMFDHZ4rUlGGxilCN0qVW969S8IbNDYMhmXpw,2539
pyarrow/include/arrow/util/math_constants.h,sha256=QIrNIKzMlaDKVOIe6tJWkPo3JtK2rDG0Ux8z7in98x0,1138
pyarrow/include/arrow/util/memory.h,sha256=wXr9qPvbRTNJNYDq92iEVGgxSzWEqAu4Eu-Kb1qHRVw,1609
pyarrow/include/arrow/util/mutex.h,sha256=jANHLdRtEDUTIoCYa7kBFRr2fTQlhkvabm1xCKkrFSY,2639
pyarrow/include/arrow/util/parallel.h,sha256=Iuj_mOxLxyuiEJRAbjrS5M8BL3W1r_lN22LHnKgwOio,3718
pyarrow/include/arrow/util/pcg_random.h,sha256=Wb4huSnThKxOkUJTPjIUZBebkjs-FlzDVbjZjK_bTzc,1285
pyarrow/include/arrow/util/print.h,sha256=AQqBOUpep2Wi1tfTvLBJeRK7kSZB2ZvJC3b1ZcelW78,1776
pyarrow/include/arrow/util/queue.h,sha256=vs-xE_jGsLwiWsCiSIshMMPxm-XKxThG6fRLoghFEYQ,1046
pyarrow/include/arrow/util/range.h,sha256=9eb1qzcKd9cv6jYlvdcpSLwB_cJgBSoN_fzDAit09kE,4989
pyarrow/include/arrow/util/rle_encoding.h,sha256=Qnlo6KMgjzIaqX2FDVlJM-ODe_H669Zo4LpgZpdsx0g,31865
pyarrow/include/arrow/util/simd.h,sha256=DkJcg2fA0D35dAJjWejqo_OykNy_an2MFCnHHPweqdE,1271
pyarrow/include/arrow/util/small_vector.h,sha256=aP9xti38tT1JxLqyyTfa1iE-K8UUFitVkz9GEnOtPJk,14932
pyarrow/include/arrow/util/sort.h,sha256=z6QTFJTKranZSLz3ov_7cgdAUUCRBPOVpz6juqPsmiY,2544
pyarrow/include/arrow/util/spaced.h,sha256=GxDNNOKr_wFJ77bmwFPzxLFQxT-EHqreUOYTVkqiHEs,3665
pyarrow/include/arrow/util/stopwatch.h,sha256=wjaUvLLMmPxAMo010wde0aDkSutbaRuptkvxlD0_PHM,1449
pyarrow/include/arrow/util/string.h,sha256=S6lPo2lgl5Xo9B4DasPmD7dGYf3iT-fzROr7hlZ8Ovw,3438
pyarrow/include/arrow/util/string_builder.h,sha256=KG1Kq6wAqqkKklEN7ttczbkmckvesmuQLjkABVEP85A,2530
pyarrow/include/arrow/util/task_group.h,sha256=ndcevZLV91JlhUjTy0STS6vgfrnlNIds6PH9CogPbY8,4468
pyarrow/include/arrow/util/tdigest.h,sha256=5WA74rC1mYHVQGMUZLm3y8mAkEQuR4nLCvoG_t8z11c,3156
pyarrow/include/arrow/util/test_common.h,sha256=di1zrj8WqO82vFlPxZicFqLgm45YD6npiruZrVGEWxM,2927
pyarrow/include/arrow/util/thread_pool.h,sha256=91QTlz_cTq1RzXPHzyANH6Scwy628YO0azX1Nm9VcLg,20859
pyarrow/include/arrow/util/time.h,sha256=Qas0nobSX9zpGDHTVkEy4NiRTZmapE6huDafS4B-b9w,3071
pyarrow/include/arrow/util/tracing.h,sha256=GDnUwNVIUGUJt7vCoyjSu8oYG65kolawu4KGZvkyTdg,1207
pyarrow/include/arrow/util/trie.h,sha256=B9ei9sN4XgL857xEyF0Ho5EGnqjrNKxJDgkXqqHcS8U,7364
pyarrow/include/arrow/util/type_fwd.h,sha256=280BeaZUwyJIrJasJICHnZ7NJbc9Ie_SWzl-wS7AYFQ,1498
pyarrow/include/arrow/util/type_traits.h,sha256=s1CoxKll91XgTlUJLQObKZHN3vJM-VX2WakWoXvzJZk,1777
pyarrow/include/arrow/util/ubsan.h,sha256=Mu-ofoat82ADXdGFwToTQEeXVLfP1kXBU4vF-7bNEvc,2865
pyarrow/include/arrow/util/unreachable.h,sha256=fge9lWPm83QAcQjaN0lAz7UTq-NQ4JWM88k31eBTxck,1001
pyarrow/include/arrow/util/uri.h,sha256=5gYIUKZa6SFvZzMe3U51DqEV0MIH-JLy0L2ZUK4_6LE,3759
pyarrow/include/arrow/util/utf8.h,sha256=iPyfRbzjohfqDb83QzosvkGW97XckuvCLrZgzj6plPo,1847
pyarrow/include/arrow/util/value_parsing.h,sha256=haeGfoDfrX6S4Fn1YmOgv1O_WN2n61nhvQ27WY8SId4,30445
pyarrow/include/arrow/util/vector.h,sha256=vaI7wks5QO5VKNwrcrDDp0UqNB66t8ph9aBFU5wSKVQ,5878
pyarrow/include/arrow/util/visibility.h,sha256=LTl38uvPQswWq9fZSW-mghHVKBkQmPTb7nuI-8uj0-U,2586
pyarrow/include/arrow/util/windows_compatibility.h,sha256=9oi_Dp7AgPzI406Y0IaU_tQglIearPskGPiO-OCGBco,1286
pyarrow/include/arrow/util/windows_fixup.h,sha256=aJSvi7BbCvJtXul8F5otHPZMq8BPIhSaQ1qGshbB2yU,1431
pyarrow/include/arrow/vendored/ProducerConsumerQueue.h,sha256=kQtEadAfKO3qYjALi1ywgCaE0ov2aGQBUFdMS1qQC48,6318
pyarrow/include/arrow/vendored/datetime.h,sha256=at3-JuEjiMAFkuwGUq4dRkC5oBSG0GOGJYbDBJ9JTGk,1043
pyarrow/include/arrow/vendored/datetime/date.h,sha256=j-pWe3QVcGyHyyp1E9UFMBG4CSVcLGuZBSBobHMeja0,245814
pyarrow/include/arrow/vendored/datetime/ios.h,sha256=I_2bJeHES2eSm9BuNxZCMHtboJTVlp8676k7yc0Lj44,1732
pyarrow/include/arrow/vendored/datetime/tz.h,sha256=xm9aHDGTT09sai3qmvQFVzZ4bBa3wXDCU9hs8SrL34M,87670
pyarrow/include/arrow/vendored/datetime/tz_private.h,sha256=g_zzjyLdFUfPP0xrQ_rNa2VrYhlE2prmLXLwVZ8WZkU,11067
pyarrow/include/arrow/vendored/datetime/visibility.h,sha256=js46GSqWXi_VKSUTEYCFTuzHLFzL_4-yCW0l_IZGO3Y,977
pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h,sha256=cgneNFt8FLkcvvoBN3rbpF133JAQYh8qRh-4T0ficd0,4384
pyarrow/include/arrow/vendored/double-conversion/bignum.h,sha256=1YwHfxcbXNG3bsb02EkNaIkYHzCUwDK0YHVsZVBvBXA,5624
pyarrow/include/arrow/vendored/double-conversion/cached-powers.h,sha256=EdkJv37VlMwcdm1ilb40ExBn7ZY4lFe2aw5VEamc8g4,3091
pyarrow/include/arrow/vendored/double-conversion/diy-fp.h,sha256=-npSYqey6grHIO69JEkjsK7CyF1XreSB0tTGAtFMVSU,4204
pyarrow/include/arrow/vendored/double-conversion/double-conversion.h,sha256=KQbmbW2cR7C-ulrsEQbICyJVVvACPPb1M6_KSWmcpE4,29194
pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h,sha256=jYPB7VTs7lCDqo0khSDNtv0qy-plSKU1-k64sH-aldY,4152
pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h,sha256=Y27GxxPhZBiN5YsQM_zf8n1jRdn4rpHQfAYT1ynxUI8,2826
pyarrow/include/arrow/vendored/double-conversion/ieee.h,sha256=et85ntHAkqY0rBkoxvxEtRFHLL6Cv_6IYbQWXn9egdk,13926
pyarrow/include/arrow/vendored/double-conversion/strtod.h,sha256=eRcP_JDKBBesK5C3TFnJMMNvGVBKGFsdovAidNj1MJ8,2254
pyarrow/include/arrow/vendored/double-conversion/utils.h,sha256=Mx6_JybemhLtFiG43T2PWNX0BEEn4eDRtySqqwj9aZI,12895
pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp,sha256=eY4o2Q9zxykS134wT2-Mflz3blJfYn46JFsRYDkYePY,21082
pyarrow/include/arrow/vendored/pcg/pcg_random.hpp,sha256=2Y_h-ra9s02ep7R2CQQ4c7cqHec1Xznwx2AeiAPG7OM,75461
pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp,sha256=pUPqr7_xnyJ7kfoQJPzRHwPAoPEX3XRW5Fou82CCeYE,29419
pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h,sha256=nco_jVnzpnDASOnTnPFE8CCj9uwOWhyV-kBgoBmnf8o,3164
pyarrow/include/arrow/vendored/portable-snippets/safe-math.h,sha256=D8WFFrtN64lU8XdNOdfSKwMMfIHxsqqbbATfuvSA1wg,49239
pyarrow/include/arrow/vendored/strptime.h,sha256=6IG4iT_rLr1Z-r7FcitdiZyEQ28slbb8r8ty5ik57N4,1247
pyarrow/include/arrow/vendored/xxhash.h,sha256=ExWtVPlfaed4_G728m0ZVyo_x78z9lx0E_-aZ_MB0l4,862
pyarrow/include/arrow/vendored/xxhash/xxhash.h,sha256=XOZ95FS3cSYTKiTDNoC6NYNCO_tl54aA5CdBNCAaVOE,189802
pyarrow/include/arrow/visit_array_inline.h,sha256=POp-FsMXfXtRce7oJk5NtZS0sR_Aic-vYIfnDHSkq5A,2252
pyarrow/include/arrow/visit_data_inline.h,sha256=o0Xy3Gp6D1749h6afD9u4RloVj8SSGH1AbmPip4tnEw,11691
pyarrow/include/arrow/visit_scalar_inline.h,sha256=MyYVwvJC8dReb95M8Dfm-GJnjKRWOh496p20wU4Al7k,2181
pyarrow/include/arrow/visit_type_inline.h,sha256=Q7PqOSFfk3BK49kp887ZUhW9xy_KHf0WIHh2azq5cac,3710
pyarrow/include/arrow/visitor.h,sha256=mRcUJU9C75pSxdz0_pNEkDjV986qbUHamhy43UVWTis,7716
pyarrow/include/arrow/visitor_generate.h,sha256=R7d9FYrqv_qI7SYNwa6ugfV_tZ5IffvK5vc38TM110E,3042
pyarrow/include/parquet/api/io.h,sha256=D0-NRwdb1FrXH8DKZsjZ8MtfZjjUaboR_lv1iFLbr2A,867
pyarrow/include/parquet/api/reader.h,sha256=njU4e2U-9SWf-XtxKdwRqezF-hLF5dAKIWUZFmyawa0,1239
pyarrow/include/parquet/api/schema.h,sha256=rDl6YZ_-iwqcmp7t_rSADxNtiJqJtdl_bHlbOJfCtE0,876
pyarrow/include/parquet/api/writer.h,sha256=wR783UqPiqunq9PY90ae-3lCkuQlScWdFbanzal8NpI,1032
pyarrow/include/parquet/arrow/reader.h,sha256=wykVCdgJ5m-PrJJk91-FPwxhdpma_RCFst_bbIv94ro,13695
pyarrow/include/parquet/arrow/schema.h,sha256=EBNwRSV2xGo9YQcBQm7lrGrsRoKSy6dkflJlVxxjSKc,6388
pyarrow/include/parquet/arrow/test_util.h,sha256=VCv1WtTDaeUSYkepcDE_CgwNFXxbi-v0HTvCiXSCAwM,19926
pyarrow/include/parquet/arrow/writer.h,sha256=GybcmYcHAZwbM06NJozjHS3ZmicuE3SK73hYZwwutSQ,4362
pyarrow/include/parquet/bloom_filter.h,sha256=Wq0VDFEUlKJTr_AD8ukGe0n6qxSCGUYavD68FPg5mbM,9708
pyarrow/include/parquet/column_page.h,sha256=9KmCb4Y1CIrWG5S3JfleT5yotDLav1V3EnEI_N0Ev1U,5949
pyarrow/include/parquet/column_reader.h,sha256=_oAg9V9caumjyEwXOA71G-Xe_u1qEO6dWckq2HNrAVE,15822
pyarrow/include/parquet/column_scanner.h,sha256=fzmNKVAw4uirtdP1DjG4Alf6_VsMZ9b99RJLfYlk9eU,9065
pyarrow/include/parquet/column_writer.h,sha256=G8KPauiMKLeywrNWeoxebQtglisiQkfbYOCHioVRDZk,11079
pyarrow/include/parquet/encoding.h,sha256=3lB8ZDFSot6v5FuL0dicqO1XzZZL2GXupoH0ttcqoGM,17154
pyarrow/include/parquet/encryption/crypto_factory.h,sha256=QoR2OidpvyjlSZ-E9ibT-71AYQi3u1V-b89GJLDCpeI,5912
pyarrow/include/parquet/encryption/encryption.h,sha256=fXOBGaVTAP-b5sdSQcjD6GbLp4HRdPqvaxgN33tEhr8,20162
pyarrow/include/parquet/encryption/file_key_material_store.h,sha256=JG8hUABrkObdvBYAAzHDtS87yiujFY7Viz4iuoSouf4,1320
pyarrow/include/parquet/encryption/file_key_unwrapper.h,sha256=CPX5m9CLgAVr97mihIod5orMDCBuM34qUQ2hvzU19LQ,2985
pyarrow/include/parquet/encryption/file_key_wrapper.h,sha256=zPkKJbGJOsUkPB-im-GVJ1at9dAK5aH35Wx1-Pju5w0,3649
pyarrow/include/parquet/encryption/key_encryption_key.h,sha256=rFaP7-JYS9AjXCsmi-m8DEUXSzQU-RgNlyP6zdwUZIc,2317
pyarrow/include/parquet/encryption/key_material.h,sha256=llOWzD7oglPXTkn8tgAZzwQCjiv0WtdPoegtu4BBI1w,6378
pyarrow/include/parquet/encryption/key_metadata.h,sha256=VQMAw8QoetWSUe6ikb-f77ES7F8oYpu5FX2_1EM4cIU,4122
pyarrow/include/parquet/encryption/key_toolkit.h,sha256=qKNmuxAtW_MZHYP6X9wLGl1g-hm7WD5on1r2U1QPBn4,3062
pyarrow/include/parquet/encryption/kms_client.h,sha256=7CqoTyGA1sCjMJqq6vZUjNebPE0EEJvxLAhUl7gXpm0,3269
pyarrow/include/parquet/encryption/kms_client_factory.h,sha256=O8OXCKSlenezOu0VbPFK6oUIDCmyGl3xmePDQx1H2OE,1359
pyarrow/include/parquet/encryption/local_wrap_kms_client.h,sha256=coi9TcmwM0wEs6GTDeQiPKTNndcxfuQEoXv4kWM6MWA,4076
pyarrow/include/parquet/encryption/test_encryption_util.h,sha256=v1uj-EZFhOPfSrVL8UYv0tSbDig-ZMPuCsOyXBr1pg8,4557
pyarrow/include/parquet/encryption/test_in_memory_kms.h,sha256=vaL04gvOB1DAl_8pvS0pQp5g7QOgFqGgR9LWYpN8f-o,3296
pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h,sha256=j8yB3LrHmezDFa7AOmpyyhKWRJW7MsS4KMPHkXapVes,5260
pyarrow/include/parquet/exception.h,sha256=vPPNX8_oD-VruhpC7H6x97gwQlyBki42uDRPtVvgqJc,5755
pyarrow/include/parquet/file_reader.h,sha256=-dXC0iiErLKvsRud9Ap_EOmKy0lcKEUCLiUxdhl_f8w,7810
pyarrow/include/parquet/file_writer.h,sha256=-vYfT-WwcchLE6giRN72EiPaR0j_OV1Lnn2EKtK-Ei4,8900
pyarrow/include/parquet/hasher.h,sha256=U_MjxUytmBAShaxjn5ua9LaEYV55yss2fljLU38N5zg,2473
pyarrow/include/parquet/level_comparison.h,sha256=1BDLfaZXK70jNH-kqUIhqPELf4Ru48QAZfmQ6WoywE0,1372
pyarrow/include/parquet/level_comparison_inc.h,sha256=BjJjr4CUinYmxh_b9LlIvwmS7P7zfBOVUwCky9HazYQ,2609
pyarrow/include/parquet/level_conversion.h,sha256=v9QAmt7SpPUkZkzd6HpA3GwGqrMbaZg0boou-trkfYM,8923
pyarrow/include/parquet/level_conversion_inc.h,sha256=DV4dLDr1m714vR7WjT_lCWRg5AnTOPFAHfJvl6tkln4,14555
pyarrow/include/parquet/metadata.h,sha256=K_gIlBEdb1U8mkBAArKh_uS-CdB11MiwrAAoIkaoA9g,19222
pyarrow/include/parquet/murmur3.h,sha256=PINZ6MrP6r6t8OdfPujzFlT5Ac0fpP1SBHTde4XMTgA,2082
pyarrow/include/parquet/parquet_version.h,sha256=I7Rzaohad38VnX0zzW8FWW7jHt4lOENTvIfWvrgHDFk,1195
pyarrow/include/parquet/pch.h,sha256=GCCeQqUf1FYv0rA70WfR3Q9wS_3STE5un5tTgRuOjEg,1277
pyarrow/include/parquet/platform.h,sha256=4cmSShaQoI0o5ZJ1DX2fSO9ALhEDov8zurwO8CKG2go,3881
pyarrow/include/parquet/printer.h,sha256=g4pHHvQ5loK9o1py7wyMJMJ7y1ceDSVY_K44Ymy_Cb0,1586
pyarrow/include/parquet/properties.h,sha256=yqHIpZZ6aPW5ldGhoTXMCpo4AgwmYeIWKw6oNbVPVuU,32982
pyarrow/include/parquet/schema.h,sha256=VAuQtP8GVlnpGMU-s-Y3EKnth8gbu9bZ_jjP1s1lJvU,18610
pyarrow/include/parquet/statistics.h,sha256=p5Rj5d_lA5xszXfjrmsZYjnR4YEMQACQOpO8I4dK2vo,14876
pyarrow/include/parquet/stream_reader.h,sha256=3AwNGq5YJXItgyHs-GWkeVM-XKREtU8FXC-BfVRVHTc,8974
pyarrow/include/parquet/stream_writer.h,sha256=K6t2Jy1NpHNDwvounY3I1uKeZ0Crf3JrEop7em1EnXs,7745
pyarrow/include/parquet/test_util.h,sha256=Ht4Ezl_P42yRf0Fp5xgvlmYdHsEtUwfePxdYEAWN_6o,27152
pyarrow/include/parquet/type_fwd.h,sha256=qam_Ym3Tncz6Fcs-S-hzKhYjNUXio7meGhOTIb6V3bw,3085
pyarrow/include/parquet/types.h,sha256=-Xb5VcLJfaT4JpQFE6-xjjzCK5NpwfzLUI9RpJyRiko,24353
pyarrow/include/parquet/windows_compatibility.h,sha256=UqmjdtDSUHYXXSN7FfO5KqS-92ukyDG6iKOThvjcZ14,918
pyarrow/include/parquet/windows_fixup.h,sha256=tZ2zUcBzQ68HXyAn3jryfHbTEq0V0ytonzajDgt5Iic,1073
pyarrow/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/includes/common.pxd,sha256=9HafIS6iuZ9j8ZKhYkiNDLspFb5i_i8mxsd_Dv-JyYc,4685
pyarrow/includes/libarrow.pxd,sha256=h8A1SRC4C5kk2LTpj5J22T4hVTEFjv_9PVddSX6fdhQ,104289
pyarrow/includes/libarrow_cuda.pxd,sha256=aLQJdjvhFE3eIOPUXQ3AYZjs4hNNjUYZ49ZVPrTsACE,4948
pyarrow/includes/libarrow_dataset.pxd,sha256=Kswf5x5qNN-WGw2p3N13nBKPDqHMSzPfPrO7CpDmMvo,16444
pyarrow/includes/libarrow_dataset_parquet.pxd,sha256=Sr0A3LxCN4U1oyayst0gw1YhK5GO292KMnS8wXzRy1M,3806
pyarrow/includes/libarrow_feather.pxd,sha256=hIVe5CqIhUjc2nvfq3vjo1XoJm5qaXNZ3a1RWw8zS_U,2190
pyarrow/includes/libarrow_flight.pxd,sha256=RaTcnnTM0fO-zqCuV7TfoFNWPA9gti0grPv1WrHNQHw,24605
pyarrow/includes/libarrow_fs.pxd,sha256=f_2t4Eor-Owq2TV5T64Qs4R_6mksJmASaCamATijPWg,14665
pyarrow/includes/libarrow_python.pxd,sha256=XZqFox7NdDCqtW_dPQB-kLuBT4313UT_VhCddPWuyRY,11551
pyarrow/includes/libarrow_substrait.pxd,sha256=e6A2CQnPtfS64xE0L3TepJ8eoy9n4n43TGwXcwMv0bk,2393
pyarrow/includes/libgandiva.pxd,sha256=QK0IrG-bXrJOJTDrc8Fjo9FaCeHOW9OFxLyxTPqdOOg,11545
pyarrow/includes/libplasma.pxd,sha256=38A4OXHbJZq3CFp4BaCCXFtKUIO-qWeBwia7Igxaa6I,1120
pyarrow/io.pxi,sha256=kpTDfwVwNcctJDx4dg4S7RJbxU3Djf2F36lVJB5TWWg,72153
pyarrow/ipc.pxi,sha256=VV0Iu7M1N-Sg3t0ommsBWo80-lHRkkFb7G-Wk6wjOO0,34090
pyarrow/ipc.py,sha256=bu4VftZH5qni-YwtpEjXjN-ME0WGDWzefoNuxbGo_rA,10191
pyarrow/json.py,sha256=WxkWLvsFNvLvhTKM1s_kRFJfPR23LiJkh3RZoQ2lFoA,877
pyarrow/jvm.py,sha256=hOBj7jSA1E45ZdqMMehr91Tgvqwo-XFHEPKttESHWO0,9928
pyarrow/lib.cp310-win_amd64.pyd,sha256=sQy-ngvK0r7ZZTPzH8mgW3xE3hma6pfhHqdqTpcIXtE,2392064
pyarrow/lib.pxd,sha256=pW_7DfDZvY-QBDEDnOsGHlgJiagZOuo60802FbDaCNU,15405
pyarrow/lib.pyx,sha256=dqQJZkSjxxQ5ebgI4tR1HoTNacpyu-PhPGZ6QjwXv8o,4931
pyarrow/lib_api.h,sha256=rDZirKq4S3ddOGcxq22FtQErrojmls_vYEn9Ky18fOA,19474
pyarrow/memory.pxi,sha256=6qNamHBm8o72PSkAwGOJQk9Mg91gwX_KyiCHrMBCiuM,8503
pyarrow/msvcp140.dll,sha256=mePiXNpAQoP72Wslt2g6jSE-eVRnSt76InkSOo0HAf0,627992
pyarrow/orc.py,sha256=C8VJZAdCLraqOyroU5OPmDkia94A_mIFhJbwwLg_oNk,12833
pyarrow/pandas-shim.pxi,sha256=lyjvCXT8mBYlTnQ9hWU5d2heUYXheeBY-p9AKaAO-Zk,8241
pyarrow/pandas_compat.py,sha256=cj6732u6g7-8yWySPiikpVJ9HI9SFh7e80p_LM6LQnQ,45065
pyarrow/parquet.dll,sha256=f4jDrfEYoU2NH3WmsDQHK7HJngFS7Zu2q5h6gMM5aXE,4853760
pyarrow/parquet.lib,sha256=BK-APFJk6ksgwdu5JTHs6vMt65br69CEKX79qoOMf00,584456
pyarrow/parquet/__init__.py,sha256=MP8hmY5y1-LXoEUg6VinrGfThZkw1HAwUIL4ddrSAxQ,842
pyarrow/parquet/__pycache__/__init__.cpython-310.pyc,,
pyarrow/parquet/__pycache__/core.cpython-310.pyc,,
pyarrow/parquet/__pycache__/encryption.cpython-310.pyc,,
pyarrow/parquet/core.py,sha256=rC_iqH2QOjyM_zyOkeh6Dkl-Q2fCAeD5IjiveQMbdS8,137377
pyarrow/parquet/encryption.py,sha256=8XjYgPw3KhU7eYuXj23pO3dJArm97v-1AAHt4WCfA3A,1176
pyarrow/plasma.py,sha256=PvFPLG6KYUMQeisPxHWEPKF4ClQ5mUtpfOu8dNoc7Bo,6612
pyarrow/public-api.pxi,sha256=-KboEjvslJ3JAYlKQkNEDtO5ZEe8zBm1QwPZsTSviJY,13249
pyarrow/scalar.pxi,sha256=zFvd4-GM8yDyGmy-kzMqYM6t4KPWNyhK5mvtR5MDiCQ,30917
pyarrow/serialization.pxi,sha256=kgFH0shM-j1ZBHVr1Bpr6KPtJMsYOunpqGsAHJYVMYQ,19643
pyarrow/serialization.py,sha256=2JC7YdswqEW43ipj-BqPegDhYy-F2KQaLC3GDkX0Fds,18706
pyarrow/src/ArrowPythonConfig.cmake.in,sha256=mjmb5oWS3D7PwD6ONAjV_2AgA9qNI-jih3N6AjafG84,1559
pyarrow/src/ArrowPythonFlightConfig.cmake.in,sha256=v4f-8ZXdMgWQXhJRfLTWT_9FqunDVoxuMR2HHnGMVLs,1603
pyarrow/src/CMakeLists.txt,sha256=bJ8DGLAfbVb06Jl_xM5q8m6alGbQcBoD7DSAtptEryY,10664
pyarrow/src/arrow-python-flight.pc.in,sha256=sPzxntgz23OlPXJK7HtCfa1bWVheEn800gPRUBa_g7U,1115
pyarrow/src/arrow-python.pc.in,sha256=oMNnK9wfIQwQJYwkgSgVd-Cf3HZvZXDbR2M-k18TNLk,1123
pyarrow/src/arrow/python/CMakeLists.txt,sha256=eDpRoeeAadQQ6zb930iuhKhcE9sPK0zxJFrn3usEFbo,846
pyarrow/src/arrow/python/api.h,sha256=biQIS5I3je_V8G078KUnuhQfdUp1lYZSkqlrNvm9I_A,1252
pyarrow/src/arrow/python/arrow_to_pandas.cc,sha256=4uRzl9ubU882chb-IBAgXm7ul8bBYeyd5_Si-uFb8mE,84744
pyarrow/src/arrow/python/arrow_to_pandas.h,sha256=K_gBYCLovD5C0WrislmQoFIwVv1LPOpYPCwPDjM2_Z4,4574
pyarrow/src/arrow/python/arrow_to_python_internal.h,sha256=DbOoJJai8YeS1JKBCloQlgzztwv1Kkjv3rF86nUK7ww,1789
pyarrow/src/arrow/python/benchmark.cc,sha256=-bPstUW6eQ843nOduN6dFYUfYaWeJlL8PctHJZkRQYI,1331
pyarrow/src/arrow/python/benchmark.h,sha256=OqCsqRe5osY2lhe2ecN8cXrdkmSFgLp6fXcf4_N9Fik,1228
pyarrow/src/arrow/python/common.cc,sha256=7X4w-VcO186pQeOESXn2yBfDFUSWStcI93VFKF5CaTY,6476
pyarrow/src/arrow/python/common.h,sha256=JaaPMEZUWBbkd-ShpaAaLejpcZ7JC0MKZfhl0q9KKb8,11659
pyarrow/src/arrow/python/csv.cc,sha256=sosW1I3yNLLmoScv8o8ECYzLUB8ZiEboeDAstC8SVZg,1865
pyarrow/src/arrow/python/csv.h,sha256=R_wZpPdMCNgw1ivJI7W2IKiuQcGxBZWK4RLjPDApkUY,1439
pyarrow/src/arrow/python/datetime.cc,sha256=IVXFMecwJaLRlSLfXCMAYllUgkq00_zSDKkH5yDOzdA,24281
pyarrow/src/arrow/python/datetime.h,sha256=umPCbKpVvH9Ri7qZBzg3R6VfrL0EQVbLRJQwfNaglpQ,7462
pyarrow/src/arrow/python/decimal.cc,sha256=WePBm0k0uHb6nRaXueXjfW5ONlBjScQAuTwo9d_kqtE,9094
pyarrow/src/arrow/python/decimal.h,sha256=BAt3laodbJkc9CsIEN0Qi1l2Ra9Fuznv1wNc5d4r8gw,4854
pyarrow/src/arrow/python/deserialize.cc,sha256=N74AdbDuMzqu1-fa4rfvxowDGRtdTZC9loggkoxy5i4,19440
pyarrow/src/arrow/python/deserialize.h,sha256=LYP0t8DhzevH9IQ7A-xyF391ZdDIMcwY-N-U8k0l1as,3995
pyarrow/src/arrow/python/extension_type.cc,sha256=KiLQVIWLw0gTtK-IZVVoRlkaQbyrZp_9sUxUHafkxIA,7059
pyarrow/src/arrow/python/extension_type.h,sha256=A7kopjTlpX3yI94b2aub-kOnjRq0VaC6ocRuWYJCHC0,3240
pyarrow/src/arrow/python/filesystem.cc,sha256=lJGAWRWBPRdeNabL-BWgFEGrG6VLAoOIGr6EQNfP3yk,6358
pyarrow/src/arrow/python/filesystem.h,sha256=y-UyYdHItSA7Hm0JOlYxqNzN8iNOc8L_tx_iXOfRbhY,5116
pyarrow/src/arrow/python/flight.cc,sha256=EP6_A70spwHql4JGnX2xqHyGScQzS6aNOOY4yE5sMLg,14383
pyarrow/src/arrow/python/flight.h,sha256=CV9HoK-GlwD5sCCn2YVByTfbUET0cRghn9GQGpLLlLI,14619
pyarrow/src/arrow/python/gdb.cc,sha256=8FwEgiz6nIrWHi2x6bahR47tYSjEUFRYllMYjJnOYaU,23863
pyarrow/src/arrow/python/gdb.h,sha256=VN8aDtc1iFPIKsjFQ29RKQOkClvKAAI_KeTy_LFZcZg,1001
pyarrow/src/arrow/python/helpers.cc,sha256=OdT95NahYTQzndGu0mcDF6r80W1LPEzMC7CCcfJppTk,16309
pyarrow/src/arrow/python/helpers.h,sha256=JMORNQnrVGfpTAegLwyUJpzhROmQB29EyI33c4eQVpg,5600
pyarrow/src/arrow/python/inference.cc,sha256=R-TeykdCtfmfSwWl_JycZx_0HJWIHqRZq6LrOZH1YWk,24155
pyarrow/src/arrow/python/inference.h,sha256=w8eRO1s9YA5L0kBffyPV5SS25ZIuh84t2JKB0C0PT2I,2102
pyarrow/src/arrow/python/init.cc,sha256=MtNxfuYH5pC7JhjMGC5jZYVJatC5kYZBTdEeLRhPiPI,1046
pyarrow/src/arrow/python/init.h,sha256=Dfdy6kMtFHc_NW6EhionR0ZWr-S6XpaICdu1PzTl-E0,974
pyarrow/src/arrow/python/io.cc,sha256=AK4tPqmnIfhd3CiMmgfHalpmxq6e21gv7AC-4in-1pI,12217
pyarrow/src/arrow/python/io.h,sha256=byOtFjW3NuetpJIErXFG62bsim0ZsWIAPgbes_JvI3k,3979
pyarrow/src/arrow/python/ipc.cc,sha256=jYOozMch7ZIQ-GD6XGP-5_gm57whFbCH36m7J4YtD-Q,2060
pyarrow/src/arrow/python/ipc.h,sha256=8oyMIbSQmMCZSUoy1K_oNGp647U5e2eUcfiVIiZqpaA,1716
pyarrow/src/arrow/python/iterators.h,sha256=7JRvhoQL0V7lOPGxhCk_1j_wUl_Vc9jDIOf5nhU1Yig,7387
pyarrow/src/arrow/python/numpy_convert.cc,sha256=SJIj2UelqBDAtpParOKDQtm5Q2EZUJHeRmFGBvj5xjk,21705
pyarrow/src/arrow/python/numpy_convert.h,sha256=PlXV4R8P8fQVVIlbersPECXM-K3F4uIWEUmiEgmTMKU,4900
pyarrow/src/arrow/python/numpy_internal.h,sha256=P8rFvhCopB5MA-rIWjiScI_6wM1Hs3GTUOD3h0Oj__0,5254
pyarrow/src/arrow/python/numpy_interop.h,sha256=p-rj6NaZbniXswmkuquLS2L1DsUGq-GsbRXqxzw2sq4,3220
pyarrow/src/arrow/python/numpy_to_arrow.cc,sha256=dtxb2QzOtNgWfm-ytOAF_Q89iLbwPzcZ6StstPGRsog,31028
pyarrow/src/arrow/python/numpy_to_arrow.h,sha256=-JhqPEg-tOBKhwRDhurxrAYQ6I-ws4CoghILzj9R-ms,2832
pyarrow/src/arrow/python/parquet_encryption.cc,sha256=RFWN7nVzsScOor6yQWkGHK9YCeDIXOrHa4bGBdyNGBE,3665
pyarrow/src/arrow/python/parquet_encryption.h,sha256=KS0h5-2nShhr19UbLmiVEFI_8gkeFnYxZk_COPkNdzM,4187
pyarrow/src/arrow/python/pch.h,sha256=cuGs6XHl1WaiL-RuVk8stRK6hquHo5mjD6z0QsGQ_uo,1153
pyarrow/src/arrow/python/platform.h,sha256=u7SUqHP7XrkMBCvXQT5kk8cBYVgZxoT7l9TqHCe19yM,1296
pyarrow/src/arrow/python/pyarrow.cc,sha256=8Vh6k8fAA1KPmgNb3FYk1u268L1_2vYNObBpNaGM3v4,3553
pyarrow/src/arrow/python/pyarrow.h,sha256=IfKMw4LulYTf2k_FvwVAbYw6Cuf3BEDuo6LdKWivtY0,2608
pyarrow/src/arrow/python/pyarrow_api.h,sha256=cmCIRRBTzAn13xc2OvSneNKyb5DFE1ui2l-yfsHPGg4,20346
pyarrow/src/arrow/python/pyarrow_lib.h,sha256=-frs4Snxe4UaZ0iYIk7NsL10uRXWbV08uy0zVge370Y,4987
pyarrow/src/arrow/python/python_test.cc,sha256=zd9F4ijhWkOOwhso44x-R9qw1ohtg0Xa-4aoAAusXb0,31629
pyarrow/src/arrow/python/python_test.h,sha256=kdEUk3TAKggBdER-tT8y-fIcO2UnfEh2K8XFrnDYLYk,1237
pyarrow/src/arrow/python/python_to_arrow.cc,sha256=ttpvTkGq0ZnziCsE9Z_DRzrdneSSfGzsNQv7ATZ4iBU,44012
pyarrow/src/arrow/python/python_to_arrow.h,sha256=-LwRqP3EowR32figOboT7L8QkCoqmrajv_PN9WzxGTI,2601
pyarrow/src/arrow/python/serialize.cc,sha256=UVzreKB-U92ovU885lMwHGkkFZ8xmlBIChz9I8V8bwc,33465
pyarrow/src/arrow/python/serialize.h,sha256=bLDzKvtqPt9fOZ0dVFO8WKGrfOa8vvbTHd-NLbTXPdQ,4540
pyarrow/src/arrow/python/type_traits.h,sha256=bhn_U-tmE9OX4GEJfFqz41Lf3tBwQPfl5wB4X3BHFQs,10443
pyarrow/src/arrow/python/udf.cc,sha256=bTB2kRAkzQLmuJBA4b2y4EI4JKWeGjVAdLFidmGVmxE,4913
pyarrow/src/arrow/python/udf.h,sha256=rdUsfJMVwl9qu6LrMMDi7QjFpqfuOwxI3zKgOOKuWwE,2213
pyarrow/src/arrow/python/visibility.h,sha256=4FcBJfEIEsf7s36wakgQL0UK0tFdHDckk3oWrOGF0eA,1378
pyarrow/substrait.py,sha256=-pnQQ28zKg3QzKVzPgNzbsVgy33WH-GpajJHcr04esI,894
pyarrow/table.pxi,sha256=H4qW1xfJLBw8I8b6iw9Zl5C8sbfX80GpMWwVv3zInbQ,167084
pyarrow/tensor.pxi,sha256=lg0936dQegSDwN7Ru64qbNdLg7m0sSuGzP1GUhVNdpo,40383
pyarrow/tensorflow/plasma_op.cc,sha256=dpVkPzUb_QyDgEURAel6ZcEZ0gaB8Orwh6_iVRZUhds,14902
pyarrow/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/tests/__pycache__/__init__.cpython-310.pyc,,
pyarrow/tests/__pycache__/arrow_16597.cpython-310.pyc,,
pyarrow/tests/__pycache__/arrow_7980.cpython-310.pyc,,
pyarrow/tests/__pycache__/conftest.cpython-310.pyc,,
pyarrow/tests/__pycache__/deserialize_buffer.cpython-310.pyc,,
pyarrow/tests/__pycache__/pandas_examples.cpython-310.pyc,,
pyarrow/tests/__pycache__/pandas_threaded_import.cpython-310.pyc,,
pyarrow/tests/__pycache__/read_record_batch.cpython-310.pyc,,
pyarrow/tests/__pycache__/strategies.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_adhoc_memory_leak.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_array.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_builder.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_cffi.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_compute.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_convert_builtin.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_cpp_internals.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_csv.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_cuda.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_cuda_numba_interop.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_cython.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_dataset.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_deprecations.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_exec_plan.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_extension_type.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_feather.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_filesystem.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_flight.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_fs.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_gandiva.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_gdb.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_hdfs.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_io.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_ipc.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_json.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_jvm.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_memory.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_misc.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_orc.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_pandas.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_plasma.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_plasma_tf_op.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_scalars.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_schema.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_serialization.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_serialization_deprecated.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_sparse_tensor.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_strategies.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_substrait.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_table.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_tensor.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_types.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_udf.cpython-310.pyc,,
pyarrow/tests/__pycache__/test_util.cpython-310.pyc,,
pyarrow/tests/__pycache__/util.cpython-310.pyc,,
pyarrow/tests/arrow_16597.py,sha256=4MtaNkHRXf-ae8UPc_W0Sy7PQOSA38L2znT2LE0YSxM,1391
pyarrow/tests/arrow_7980.py,sha256=W3Gbd_XbB9vC87k4VDIjyJgGfUurz6PKkFJn1pNdR4I,1124
pyarrow/tests/bound_function_visit_strings.pyx,sha256=kkGD3ApWvcPG8DOwVq7xb_yhsAldle9rvimeC7kr6Ms,2115
pyarrow/tests/conftest.py,sha256=d2apW0Cg3Dk0VYf3EC84fBTLX5Vcsh0CTpCo1yZfnvA,6466
pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather,sha256=qzcc7Bo4OWBXYsyyKdDJwdTRstMqB1Zz0GiGYtndBnE,594
pyarrow/tests/data/orc/README.md,sha256=ZX1AAHzJvZLsiNeciShvmfnmV6wriByQlpblyhMU3iY,956
pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz,sha256=xLjAXd-3scx3DCyeAsmxTO3dv1cj9KRvYopKe5rQNiI,50
pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc,sha256=zj0579dQBXhF7JuB-ZphkmQ81ybLo6Ca4zPV4HXoImY,523
pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz,sha256=kLxmwMVHtfzpHqBztFjfY_PTCloaXpfHq9DDDszb8Wk,323
pyarrow/tests/data/orc/TestOrcFile.test1.orc,sha256=A4JxgMCffTkz9-XT1QT1tg2TlYZRRz1g7iIMmqzovqA,1711
pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz,sha256=oWf7eBR3ZtOA91OTvdeQJYos1an56msGsJwhGOan3lo,182453
pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc,sha256=nYsVYhUGGOL80gHj37si_vX0dh8QhIMSeU4sHjNideM,30941
pyarrow/tests/data/orc/decimal.jsn.gz,sha256=kTEyYdPDAASFUX8Niyry5mRDF-Y-LsrhSAjbu453mvA,19313
pyarrow/tests/data/orc/decimal.orc,sha256=W5cV2WdLy4OrSTnd_Qv5ntphG4TcB-MyG4UpRFwSxJY,16337
pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet,sha256=YPGUXtw-TsOPbiNDieZHobNp3or7nHhAxJGjmIDAyqE,3948
pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet,sha256=7sebZgpfdcP37QksT3FhDL6vOA9gR6GBaq44NCVtOYw,2012
pyarrow/tests/data/parquet/v0.7.1.parquet,sha256=vmdzhIzpBbmRkq3Gjww7KqurfSFNtQuSpSIDeQVmqys,4372
pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet,sha256=VGgSjqihCRtdBxlUcfP5s3BSR7aUQKukW-bGgJLf_HY,4008
pyarrow/tests/deserialize_buffer.py,sha256=U_b2sEDgUyYzt2nUVNf_EBVZDsd_PUN312Bp0N-Yxkg,987
pyarrow/tests/pandas_examples.py,sha256=rMH-mMWhDC9s56E9k9qH59IvN3NhhcVHR2sZaC1GNk0,5287
pyarrow/tests/pandas_threaded_import.py,sha256=TkyeVQWGZNoRUgbx39KC5ENTV8q3bMmo9wJxIIxso0Y,1473
pyarrow/tests/parquet/__init__.py,sha256=Mfwz9kJ-i5Y10YTFCW5ygJeo0NSsIUW6taid8zVNURM,1062
pyarrow/tests/parquet/__pycache__/__init__.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/common.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/conftest.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/encryption.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_basic.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_compliant_nested_type.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_data_types.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_dataset.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_datetime.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_encryption.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_metadata.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_pandas.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_file.cpython-310.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_writer.cpython-310.pyc,,
pyarrow/tests/parquet/common.py,sha256=Z3M1z8f4j-tyDowy9A2EKC3iAr9knxCv2oOHIVqnHCM,6804
pyarrow/tests/parquet/conftest.py,sha256=Fj2MVUnMlbBIr9gNT03fK03r-Jmg4TdBvUQ4kOKyCCU,2622
pyarrow/tests/parquet/encryption.py,sha256=0Zoz4i_eIEF7SepbogLZg8dWKprEw0IjgRmh-xu_vNk,2582
pyarrow/tests/parquet/test_basic.py,sha256=3K0iKt6W4ww84myA9EWlkGJV5UcRvuqpwkyGHN_kB0I,31111
pyarrow/tests/parquet/test_compliant_nested_type.py,sha256=WEHiNiWNaFVhW01wmmusoI-DP7w9PcTG78oFpyV1xd4,4614
pyarrow/tests/parquet/test_data_types.py,sha256=SCienlrVcaMUXlIPNbgDbcd8TQm3XBY7J0yy4dG12nE,17783
pyarrow/tests/parquet/test_dataset.py,sha256=B5XBne5XFeEsD7Cv5XeOVnsfA7jkPPZ2rxDDF3-uLmU,66041
pyarrow/tests/parquet/test_datetime.py,sha256=rDQTXBegm08Sn0BSWevfzlgrCAi_3yMnpu4Mw6ip3nk,16374
pyarrow/tests/parquet/test_encryption.py,sha256=z7zZTqpjDYO5epuPgvOAJSA1exrqtd7hxjkSOQru7jQ,20778
pyarrow/tests/parquet/test_metadata.py,sha256=j2cW6MnjT0b4nUTJbx1pTc30vOlo4_552DNIkuI9GXY,21422
pyarrow/tests/parquet/test_pandas.py,sha256=VCXgttQ2XGivamFHP5rwq7OeFEUKaPmOxvAQQLqQSyE,25866
pyarrow/tests/parquet/test_parquet_file.py,sha256=aJf8owbonjs_OpA8IBxKY8Yip4o8RBBxh5mCVU51ZDs,10430
pyarrow/tests/parquet/test_parquet_writer.py,sha256=jnbcAAXaZpuANdRJx16hifM8fBkw-kui43fo5UP1QpM,10228
pyarrow/tests/pyarrow_cython_example.pyx,sha256=vYKMHQ_5fSXCkOVkOntPkHLDISOOJHYp-TfRaLMZA5U,2006
pyarrow/tests/read_record_batch.py,sha256=J8MvpGBAv7rjvoseM5OnQyU9BL_3f2Ffdc0cYFvLh60,978
pyarrow/tests/strategies.py,sha256=nNoAH1rcELm6tqo5vB-yiPM1k6ZqAQzFosT1Hadi39I,13904
pyarrow/tests/test_adhoc_memory_leak.py,sha256=tn3uWObaOgx-XdrqrHSohfEQ3p1Op62ne1iSxcO77ZE,1453
pyarrow/tests/test_array.py,sha256=pPdrn-jM9NSyrUs8bSox-7mI0ADQV_Z1yljLujIPPkQ,111264
pyarrow/tests/test_builder.py,sha256=SxqKYDo9TSsmDSx9lE8UcUG_Hq6wnpkGexOiva_Cne4,2251
pyarrow/tests/test_cffi.py,sha256=22iNmZOVbv9KAlvX5racyJl0MJ2G6qXrrNFYWEv2PXA,14050
pyarrow/tests/test_compute.py,sha256=GKfFg2Cjb_6JU8_UxIeFE_ffGwBhv-4XvoJ58UZTMII,113093
pyarrow/tests/test_convert_builtin.py,sha256=i1reqVswDT5hUv-nX0JyCtnOsn37JahEckBrtlZRp9k,74504
pyarrow/tests/test_cpp_internals.py,sha256=H_29c3XipzEXm29Vs5-slyzPLhq6xMnPoRb3gm0C6oo,1237
pyarrow/tests/test_csv.py,sha256=gpiLo_DQr3ByTjrTMioKajc2sSc6w1FJfi-ENxHw4Bs,74618
pyarrow/tests/test_cuda.py,sha256=2FUuhdmPkWwMtMDN2K20V7IT-50sw-8NHVnzp7xaz_E,28655
pyarrow/tests/test_cuda_numba_interop.py,sha256=MIdgdCZ5715r0tuklCRgv1TWSvnGf2QjJUKgGiEZVnk,8965
pyarrow/tests/test_cython.py,sha256=nwZPdSHBFagCBcAMUynMAcI7KHDxAFSDtyAMTCNHGr4,6880
pyarrow/tests/test_dataset.py,sha256=IylP53qxJQtfjJv6dsgz8Ofsp-BHrto0Fjh7-mdY32E,180341
pyarrow/tests/test_deprecations.py,sha256=qGsfYqoc2Ty5l3fIKL7zCM_IeGFgquPPv_5OWRAA6UM,914
pyarrow/tests/test_exec_plan.py,sha256=Kg_YrHHPSjYWdKD3Fm4z3D4pulczjDlx7qjSEs4aK9w,9695
pyarrow/tests/test_extension_type.py,sha256=m-sjRdKhwee5dYnpIqftatiDfEJbHiVmhS8Y__YW07k,35262
pyarrow/tests/test_feather.py,sha256=PwYPOCfaBunehykGkpMERtxSs_631GrbtU-pb1IAdKo,24966
pyarrow/tests/test_filesystem.py,sha256=FoVMpsFReHZluo_GoDitkWODrvg6Hue3l-AB_S4DraA,2454
pyarrow/tests/test_flight.py,sha256=ldPnILg0PouC4nKe-CtvUSVzE4LKu57OSnWN81vX0Qs,82622
pyarrow/tests/test_fs.py,sha256=JcFKNvp5gG-FDOf5cs9W4sRTh9LRrMeNX8DEwfl5qR0,57979
pyarrow/tests/test_gandiva.py,sha256=uCnCwNnrO4J1hQqlz3RMacN_3MZVdk06zVAfKw-cbsM,15933
pyarrow/tests/test_gdb.py,sha256=PDLghY2CwWl5BqzYI1-ScwG2CKbTfxjVvvZbKG4E1ws,44294
pyarrow/tests/test_hdfs.py,sha256=YzkPYciHVEAeUSdzTTg-fuyLkcargjODWIJA01zUFzY,13899
pyarrow/tests/test_io.py,sha256=vmIxA2E4ZDhJBidMyNuUnQvxFrjMFNekNRuK1XjW1IQ,59736
pyarrow/tests/test_ipc.py,sha256=8IjbIQM-gMX7CUT8h8Cz7wlEY0wQDIcSk_hnKjyy6t8,37473
pyarrow/tests/test_json.py,sha256=H2nTqHjMhHL132TNPkGJz5UcTZZe7dW6pGyTshpsWV0,12913
pyarrow/tests/test_jvm.py,sha256=X3o6hiM4Ui-Xe-2d_ACBC04fNgdV2Cv2ab4vY-i4S1c,15904
pyarrow/tests/test_memory.py,sha256=GqsyRmhLNBGjmeXq5ahZ2WX-X8Taf7fAnZG6PtJXu7g,8050
pyarrow/tests/test_misc.py,sha256=4UrhCfKsuSMquIq3j9MnpWuYL9s2TZ3GzkI8eTh4ZFI,6128
pyarrow/tests/test_orc.py,sha256=KK4wTDJzF-YkDPgNRODqdhxI63cwrF9Vt_J0f_7XQok,19915
pyarrow/tests/test_pandas.py,sha256=vdELeZK7CotCZKgHwjiK3U-oA1IfShR8aCpJoIwy4kA,166083
pyarrow/tests/test_plasma.py,sha256=aZ_Xbh4g2UgXUPoiuhSlhgJMGnY4Y64onW1wQu3vd9o,46019
pyarrow/tests/test_plasma_tf_op.py,sha256=URg0bM4VlTDcVGCM3_b-y5jXOl7kgEXLET5TvcYzcfE,3627
pyarrow/tests/test_scalars.py,sha256=bRcHFcxLD-0g2wJrBT9gGWya3jwCWMYjeaBYeY3_7Lk,21109
pyarrow/tests/test_schema.py,sha256=HOZ1xE9ev2xoS6vN1mRP7Oigtmo5LK4PnyRAHhjthgQ,22024
pyarrow/tests/test_serialization.py,sha256=SytDQwhtKrTgZERZPPHyfHoJW__t7tTHjMQ-5BKzyKI,43259
pyarrow/tests/test_serialization_deprecated.py,sha256=27eWR8AsmmLTh0scpfF1tvEV3t8EbWJXPGvznJh8X14,1713
pyarrow/tests/test_sparse_tensor.py,sha256=0obkMMgbJSrmG8o-B8eCC_jZpB4Q5Ebz23-LcsVzqKw,17927
pyarrow/tests/test_strategies.py,sha256=6JJifQKQVyQpJB0LqGEcQ-Qv8NyxseAMuDs-3_oKNX8,1800
pyarrow/tests/test_substrait.py,sha256=0NI45MiNbmHQDCYBFSOcMlPJrD8NLX-W3tAQc6AaW7o,8909
pyarrow/tests/test_table.py,sha256=5mUNKpj0XqO_mxNUBsFUzgc0ixoqso9wIGbly0HHwDQ,68836
pyarrow/tests/test_tensor.py,sha256=9rTiqEhp0Ck1aaZVZZ6BlTE-iqf8SyIFqOGH8BBjOPA,6486
pyarrow/tests/test_types.py,sha256=IaPwbryjv_3fucaYeDDI5_S9w_tQy60u1W9SIRBajBM,34701
pyarrow/tests/test_udf.py,sha256=X6j5Uq6Ty3X-SJn7EJE0WuHxRNDFyOgXOWJXLzVPEr8,17632
pyarrow/tests/test_util.py,sha256=D6D2BH64lfmuu0MqgUjJ9vXb0mZubYntVWDZh4BL1vU,1843
pyarrow/tests/util.py,sha256=KvhwpPwi9J8SKvsQkYRqN6PXxh7lJ8ieRaCNqKdGSvo,13919
pyarrow/types.pxi,sha256=ohj8qxmIxKSXbM5fbCUhts4K0fEsHlgqXQP448PbCqA,95645
pyarrow/types.py,sha256=tEbZYczGinZeZHupKvw_Hwi7BO1lNGtdlKZ6IbTeM2s,10931
pyarrow/util.py,sha256=Xnc5lCLxAOCkpgc4-STjhkAY9MVaPVqrvaascBZsNt4,4875
pyarrow/vendored/__init__.py,sha256=EVfaECqIP6zwm3-KeFxrnNB3-C4waxZUgr3MmUMTU6k,801
pyarrow/vendored/__pycache__/__init__.cpython-310.pyc,,
pyarrow/vendored/__pycache__/docscrape.cpython-310.pyc,,
pyarrow/vendored/__pycache__/version.cpython-310.pyc,,
pyarrow/vendored/docscrape.py,sha256=QgFpJRG3lyDx6Q0sUuqkkowQ23up97bT_LlgkqG5K40,23691
pyarrow/vendored/version.py,sha256=cr57fm516rcswYHhTfyRDXt-yDWq1EAlRTrZz3mVHo8,14890
