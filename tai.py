"""
EEG Visualizer – Enhanced Version with Improved Debug
Author: you
"""
from __future__ import annotations
import os, json, math, tempfile, threading, time, base64, io, logging, mne
import traceback
from typing import Optional, Dict, Any, List
import numpy as np
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots
from taipy.gui import Gui, State, notify, Markdown
from fpdf import FPDF
import qrcode
from datetime import datetime

# ---------------- CONFIG ----------------
if os.path.exists("logo.svg"):
    with open("logo.svg", "rb") as f:
        LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(f.read()).decode()
else:
    # Create a simple SVG logo as base64
    svg = """<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="40" stroke="green" stroke-width="4" fill="yellow" />
        <text x="50" y="50" font-size="20" text-anchor="middle" fill="black">EEG</text>
    </svg>"""
    LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(svg.encode()).decode()

# Enhanced logging configuration
class DebugHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.logs = []
        self.max_logs = 1000
        
    def emit(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).strftime('%H:%M:%S.%f')[:-3],
            'level': record.levelname,
            'message': self.format(record),
            'module': record.module,
            'funcName': record.funcName,
            'lineno': record.lineno
        }
        self.logs.append(log_entry)
        if len(self.logs) > self.max_logs:
            self.logs.pop(0)
    
    def get_logs(self, level=None):
        if level:
            return [log for log in self.logs if log['level'] == level]
        return self.logs
    
    def clear_logs(self):
        self.logs.clear()

# Initialize debug handler
debug_handler = DebugHandler()
debug_handler.setFormatter(logging.Formatter('%(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s'))

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
        debug_handler
    ]
)
logger = logging.getLogger(__name__)

# Performance monitoring decorator
def monitor_performance(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.debug(f"Function {func.__name__} completed in {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Function {func.__name__} failed after {duration:.3f}s: {str(e)}")
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            raise
    return wrapper

# ---------------- EEG UTILS ----------------
@monitor_performance
def report_action(state: EEGState):
    """Generate PDF report"""
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        state.loading = True
        state.status = "Generating report..."
        notify(state, "info", "Generating report…")
        logger.info("Starting report generation")
        
        # Ensure we have metrics
        if not state.metrics:
            qa_action(state)
        
        dest = os.path.join(tempfile.gettempdir(), f"EEG_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf")
        export_report(state.raw, state.metrics, dest)
        state.status = "Report generated"
        
        update_debug_info(state)
        notify(state, "success", f"Report saved: {dest}")
        
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        logger.debug(f"Report traceback: {traceback.format_exc()}")
        notify(state, "error", f"Report failed: {str(e)}")
        state.status = "Error generating report"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def plot_all(state: EEGState):
    """Update all plots"""
    if not state.raw:
        return
    
    try:
        logger.info("Updating all plots")
        state.plot_raw = plot_raw(state.raw, state.picks[:15])
        state.plot_psd = plot_psd(state.raw)
        state.plot_topo = plot_topo(state.raw)
        state.plot_bp = plot_bandpower(state.raw)
        state.plot_events = plot_events(state.raw)
        
        # Update event table
        if state.raw.annotations:
            onsets = [a["onset"] for a in state.raw.annotations]
            durs = [a["duration"] for a in state.raw.annotations]
            descs = [a["description"] for a in state.raw.annotations]
            state.event_df = [{"onset": o, "duration": d, "description": de} 
                             for o, d, de in zip(onsets, durs, descs)]
            logger.debug(f"Updated event table with {len(state.event_df)} events")
        else:
            state.event_df = []
            
    except Exception as e:
        logger.error(f"Plotting failed: {e}")
        logger.debug(f"Plotting traceback: {traceback.format_exc()}")
        notify(state, "error", f"Plotting failed: {str(e)}")

def toggle_debug(state: EEGState):
    """Toggle debug mode"""
    state.debug = not state.debug
    state.show_debug_panel = state.debug
    
    if state.debug:
        logger.setLevel(logging.DEBUG)
        logging.getLogger('mne').setLevel(logging.DEBUG)
        notify(state, "info", "Debug mode enabled")
        logger.debug("Debug mode activated - verbose logging enabled")
    else:
        logger.setLevel(logging.INFO)
        logging.getLogger('mne').setLevel(logging.WARNING)
        notify(state, "info", "Debug mode disabled")
    
    update_debug_info(state)

def clear_debug_logs(state: EEGState):
    """Clear debug logs"""
    debug_handler.clear_logs()
    state.debug_logs = []
    notify(state, "info", "Debug logs cleared")

def change_debug_level(state: EEGState):
    """Change debug logging level"""
    levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR
    }
    
    if state.debug_level in levels:
        logger.setLevel(levels[state.debug_level])
        notify(state, "info", f"Debug level set to {state.debug_level}")
        logger.info(f"Logging level changed to {state.debug_level}")
        update_debug_info(state)

def export_debug_logs(state: EEGState):
    """Export debug logs to file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(tempfile.gettempdir(), f"EEG_Debug_Log_{timestamp}.txt")
        
        with open(log_file, 'w') as f:
            f.write(f"EEG Visualizer Debug Log\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Debug Level: {state.debug_level}\n")
            f.write(f"Memory Usage: {state.memory_usage}\n")
            f.write("="*80 + "\n\n")
            
            for log in debug_handler.get_logs():
                f.write(f"[{log['timestamp']}] {log['level']} - {log['module']}.{log['funcName']}:{log['lineno']} - {log['message']}\n")
        
        notify(state, "success", f"Debug logs exported: {log_file}")
        logger.info(f"Debug logs exported to: {log_file}")
        
    except Exception as e:
        logger.error(f"Failed to export debug logs: {e}")
        notify(state, "error", f"Export failed: {str(e)}")

# ---------------- GUI ----------------
page = """
<|toggle|theme|>
# EEG Visualizer 🧠

<|layout|columns=1 1 1|gap=10px|>
<|file_selector|file_selector|on_action=load|label=Upload EEG|extensions=.edf,.bdf,.fif,.set|>
<|{file}|text|label=Current File|>
<|Delete|button|on_action=delete|active={not loading}|>
<|layout|>

<|layout|columns=1 1 1|gap=10px|>
<|{ref}|selector|lov=average;mastoid|label=Reference|>
<|{band}|slider|min=0.1|max=100|range|label=Band-pass (Hz)|>
<|{strict}|selector|lov=low;medium;high|label=QA Strictness|>
<|layout|>

<|layout|columns=1 1|gap=10px|>
<|{picks}|selector|multiple|lov={raw.ch_names if raw else []}|label=Channels|>
<|{debug}|toggle|label=Debug Mode|on_change=toggle_debug|>
<|layout|>

<|layout|columns=1 1 1 1|gap=10px|>
<|Preprocess|button|on_action=preprocess_action|active={not loading and raw}|>
<|ICA|button|on_action=ica_action|active={not loading and raw}|>
<|QA|button|on_action=qa_action|active={not loading and raw}|>
<|Export PDF|button|on_action=report_action|active={not loading and raw}|>
<|layout|>

<|{status}|text|style=font-weight:bold;|>
<|{loading}|spinner|>

<|part|render={debug and show_debug_panel}|class_name=card|
## Debug Panel 🔧
<|layout|columns=1 1 1|gap=10px|>
<|{debug_level}|selector|lov=DEBUG;INFO;WARNING;ERROR|label=Log Level|on_change=change_debug_level|>
<|Clear Logs|button|on_action=clear_debug_logs|>
<|Export Logs|button|on_action=export_debug_logs|>
<|layout|>

**Memory Usage:** {memory_usage}

<|part|render={performance_stats}|
**Performance Stats:**
<|{performance_stats}|table|>
|>

**Recent Debug Logs:**
<|{debug_logs}|table|columns=timestamp;level;module;funcName;message|page_size=10|>
|>

<|part|class_name=card|
## Raw EEG Trace
<|{plot_raw}|chart|>
|>

<|layout|columns=1 1|gap=20px|>
<|part|class_name=card|
## Power Spectral Density
<|{plot_psd}|chart|>
|>
<|part|class_name=card|
## Electrode Topography
<|{plot_topo}|image|width=100%|>
|>
<|layout|>

<|part|class_name=card|
## Band Power
<|{plot_bp}|chart|>
|>

<|part|class_name=card|render={ica is not None}|
## ICA Components
<|{plot_ica}|image|width=100%|>
|>

<|part|class_name=card|render={len(event_df) > 0}|
## Event Timeline
<|{plot_events}|chart|>
## Event Details
<|{event_df}|table|>
|>

<|part|class_name=card|render={metrics}|
## Quality Metrics
<|{metrics}|table|>
|>
"""

if __name__ == "__main__":
    try:
        logger.info("Starting EEG Visualizer...")
        gui = Gui(page)
        gui.run(title="EEG Visualizer - Enhanced Debug", port=5000, dark_mode=True)
    except Exception as e:
        logger.error(f"Failed to start GUI: {e}")
        logger.debug(f"Startup traceback: {traceback.format_exc()}")
        print(f"Error starting application: {e}")
        import sys
        sys.exit(1)
def load_raw(path: str) -> mne.io.Raw:
    """Load EEG data from various file formats"""
    ext = os.path.splitext(path)[1].lower()
    loaders = {
        ".edf": mne.io.read_raw_edf,
        ".bdf": mne.io.read_raw_edf,
        ".fif": mne.io.read_raw_fif,
        ".set": mne.io.read_raw_eeglab,
    }
    if ext not in loaders:
        raise ValueError(f"Unsupported file type: {ext}")
    
    logger.info(f"Loading file: {path}")
    logger.debug(f"File size: {os.path.getsize(path) / 1024 / 1024:.2f} MB")
    
    raw = loaders[ext](path, preload=True)
    
    # Log file info
    logger.info(f"Data loaded: {raw.info['nchan']} channels, {raw.n_times} samples, {raw.info['sfreq']} Hz")
    logger.debug(f"Channel names: {raw.ch_names}")
    logger.debug(f"Duration: {raw.times[-1]:.2f} seconds")
    
    return raw

@monitor_performance
def preprocess(raw: mne.io.Raw, ref: str, l: float, h: float) -> mne.io.Raw:
    """Apply preprocessing steps to EEG data"""
    raw = raw.copy()
    logger.info(f"Preprocessing: ref={ref}, bandpass={l}-{h}Hz")
    
    # Store original data stats for comparison
    orig_std = np.std(raw.get_data())
    logger.debug(f"Original data std: {orig_std:.4f}")
    
    if ref == "average":
        raw.set_eeg_reference("average", projection=True)
        logger.debug("Applied average reference")
    elif ref == "mastoid":
        try:
            raw.set_eeg_reference(["M1", "M2"])
            logger.debug("Applied mastoid reference")
        except Exception as e:
            logger.warning(f"Mastoid reference failed: {e}, using average instead")
            raw.set_eeg_reference("average")
    
    # Apply filter
    raw.filter(l, h, method='fir', phase='zero-double')
    
    # Log preprocessing effects
    new_std = np.std(raw.get_data())
    logger.debug(f"Post-preprocessing data std: {new_std:.4f} (change: {(new_std/orig_std-1)*100:.1f}%)")
    
    return raw

@monitor_performance
def run_ica(raw: mne.io.Raw, n_components: int = 20) -> tuple[mne.io.Raw, mne.preprocessing.ICA]:
    """Run ICA decomposition and return cleaned data"""
    logger.info(f"Running ICA with {n_components} components")
    
    # Pre-ICA data stats
    pre_var = np.var(raw.get_data())
    logger.debug(f"Pre-ICA data variance: {pre_var:.6f}")
    
    ica = mne.preprocessing.ICA(n_components=n_components, random_state=42)
    ica.fit(raw)
    
    logger.debug(f"ICA explained variance: {ica.explained_variance_:.2f}")
    logger.debug(f"ICA components shape: {ica.mixing_matrix_.shape}")
    
    cleaned_raw = ica.apply(raw.copy())
    
    # Post-ICA data stats
    post_var = np.var(cleaned_raw.get_data())
    logger.debug(f"Post-ICA data variance: {post_var:.6f} (change: {(post_var/pre_var-1)*100:.1f}%)")
    
    return cleaned_raw, ica

@monitor_performance
def compute_metrics(raw: mne.io.Raw, strict: str) -> dict:
    """Compute quality metrics for EEG data"""
    logger.debug(f"Computing metrics with strictness: {strict}")
    
    data = raw.get_data()
    sfreq = raw.info["sfreq"]
    
    logger.debug(f"Data shape: {data.shape}")
    logger.debug(f"Data range: [{np.min(data):.6f}, {np.max(data):.6f}]")
    
    # Calculate metrics
    snr = np.mean(np.abs(data)) / np.std(data)
    flat = np.sum(np.var(data, axis=1) < 1e-6)
    kurt = np.sum(
        (np.mean((data - data.mean(1, keepdims=True)) ** 4, 1))
        / (np.var(data, 1) ** 2)
        > 10
    )
    
    # Line noise detection
    fft = np.abs(np.fft.rfft(data, axis=1))
    line_freq_idx = int(49/sfreq * fft.shape[1])
    line_freq_idx_end = int(51/sfreq * fft.shape[1])
    line = np.mean(fft[:, line_freq_idx:line_freq_idx_end])
    
    # Thresholds based on strictness level
    thr = {
        "low": dict(snr=0.5, flat=5, kurt=20, noise=10),
        "medium": dict(snr=1.0, flat=3, kurt=15, noise=5),
        "high": dict(snr=2.0, flat=1, kurt=10, noise=2),
    }[strict]
    
    logger.info(f"Metrics computed: SNR={snr:.2f}, Flat={flat}, Kurt={kurt}, Noise={line:.2f}")
    logger.debug(f"Thresholds used: {thr}")
    
    # Additional debug metrics
    metrics = {
        "SNR": round(snr, 3),
        "Flat Channels": int(flat),
        "High Kurtosis": int(kurt),
        "Line Noise": round(line, 3),
        "Passes SNR": snr >= thr["snr"],
        "Passes Flat": flat <= thr["flat"],
        "Passes Kurtosis": kurt <= thr["kurt"],
        "Passes Noise": line <= thr["noise"],
        "Data Range": f"[{np.min(data):.2e}, {np.max(data):.2e}]",
        "Data Mean": round(np.mean(data), 6),
        "Data Std": round(np.std(data), 6),
        "Sampling Rate": f"{sfreq} Hz",
        "Duration": f"{raw.times[-1]:.2f}s",
        "N Channels": len(raw.ch_names),
    }
    
    # Overall quality assessment
    quality_checks = [
        snr >= thr["snr"],
        flat <= thr["flat"],
        kurt <= thr["kurt"],
        line <= thr["noise"]
    ]
    metrics["Overall Quality"] = "Good" if all(quality_checks) else "Poor"
    metrics["Quality Score"] = f"{sum(quality_checks)}/4"
    
    return metrics

def qr_code(data: str) -> str:
    """Generate QR code as base64 image"""
    try:
        img = qrcode.make(data)
        buf = io.BytesIO()
        img.save(buf, format="PNG")
        return "data:image/png;base64," + base64.b64encode(buf.getvalue()).decode()
    except Exception as e:
        logger.error(f"QR code generation failed: {e}")
        return ""

# ---------------- PLOTTING ----------------
@monitor_performance
def plot_raw(raw, picks, start=0, window=10, decim=10):
    """Create interactive raw EEG trace plot"""
    logger.debug(f"Plotting raw data: {len(picks)} channels, {start}-{start+window}s, decim={decim}")
    
    data, times = raw[picks, start * raw.info["sfreq"] : (start + window) * raw.info["sfreq"]]
    times = times[::decim]
    
    logger.debug(f"Plot data shape: {data.shape}, times shape: {times.shape}")
    
    fig = make_subplots(rows=len(picks), cols=1, shared_xaxes=True, vertical_spacing=0.02)
    
    for i, (ch, ch_data) in enumerate(zip(picks, data)):
        fig.add_trace(
            go.Scatter(
                x=times,
                y=ch_data[::decim],
                mode="lines",
                line=dict(width=1, color=px.colors.qualitative.Dark24[i % 24]),
                name=ch,
                hovertemplate=f"{ch}<br>Time: %{{x:.2f}}s<br>Amplitude: %{{y:.2f}}µV",
                showlegend=False
            ),
            row=i+1, col=1
        )
        fig.update_yaxes(title_text=ch, row=i+1, col=1)
    
    fig.update_layout(
        title=f"EEG Raw Trace ({start}-{start+window}s)",
        hovermode="closest",
        height=150 + 50 * len(picks),
        template="plotly_dark",
        xaxis=dict(title="Time (s)", rangeslider=dict(visible=True)),
    )
    
    return fig

@monitor_performance
def plot_psd(raw):
    """Create power spectral density plot"""
    logger.debug("Computing PSD...")
    
    psds, freqs = mne.time_frequency.psd_welch(raw, fmin=0, fmax=60)
    
    logger.debug(f"PSD shape: {psds.shape}, freq range: {freqs[0]:.2f}-{freqs[-1]:.2f} Hz")
    
    fig = go.Figure()
    for i, ch in enumerate(raw.ch_names[:10]):  # Limit to 10 channels for readability
        fig.add_trace(go.Scatter(
            x=freqs, 
            y=psds[i], 
            name=ch,
            opacity=0.7,
            hovertemplate=f"{ch}<br>Frequency: %{{x:.1f}}Hz<br>Power: %{{y:.2e}}µV²/Hz"
        ))
    
    fig.update_layout(
        title="Power Spectral Density",
        xaxis_title="Frequency (Hz)",
        yaxis_title="Power (µV²/Hz)",
        template="plotly_dark",
        height=400,
        legend=dict(orientation="h", yanchor="bottom", y=1.02)
    )
    
    return fig

@monitor_performance
def plot_topo(raw):
    """Create electrode topography plot"""
    try:
        logger.debug("Creating topography plot...")
        fig = raw.plot_sensors(show=False, sphere=None, kind='topomap')
        buf = io.BytesIO()
        fig.savefig(buf, format="png", dpi=150, transparent=True, bbox_inches='tight')
        plt.close(fig)
        b64 = base64.b64encode(buf.getvalue()).decode()
        logger.debug("Topography plot created successfully")
        return f"data:image/png;base64,{b64}"
    except Exception as e:
        logger.error(f"Topo plot failed: {e}")
        logger.debug(f"Topo plot traceback: {traceback.format_exc()}")
        return None

@monitor_performance
def plot_bandpower(raw):
    """Create band power bar chart"""
    logger.debug("Computing band power...")
    
    psds, freqs = mne.time_frequency.psd_welch(raw, fmin=0.5, fmax=40)
    bands = {
        "Delta": (0.5, 4),
        "Theta": (4, 8),
        "Alpha": (8, 13),
        "Beta": (13, 30),
        "Gamma": (30, 40)
    }
    
    band_powers = {}
    for name, (fmin, fmax) in bands.items():
        mask = (freqs >= fmin) & (freqs < fmax)
        power = psds[:, mask].mean(axis=1)
        band_powers[name] = power
        logger.debug(f"{name} band ({fmin}-{fmax}Hz): mean power = {np.mean(power):.4e}")
    
    fig = go.Figure()
    for name, power in band_powers.items():
        fig.add_trace(go.Bar(
            name=name,
            x=raw.ch_names,
            y=power,
            opacity=0.7
        ))
    
    fig.update_layout(
        title="Band Power by Channel",
        barmode='group',
        template="plotly_dark",
        height=400,
        xaxis_title="Channel",
        yaxis_title="Power (µV²/Hz)"
    )
    
    return fig

@monitor_performance
def plot_ica(ica, raw):
    """Create ICA components plot"""
    try:
        logger.debug(f"Creating ICA plot with {ica.n_components_} components...")
        fig = ica.plot_components(show=False, sphere=None)
        buf = io.BytesIO()
        fig.savefig(buf, format="png", dpi=120, transparent=True, bbox_inches='tight')
        plt.close(fig)
        logger.debug("ICA plot created successfully")
        return f"data:image/png;base64,{base64.b64encode(buf.getvalue()).decode()}"
    except Exception as e:
        logger.error(f"ICA plot failed: {e}")
        logger.debug(f"ICA plot traceback: {traceback.format_exc()}")
        return None

@monitor_performance
def plot_events(raw):
    """Create event timeline plot"""
    if not raw.annotations:
        logger.debug("No events found in raw data")
        return None
    
    logger.debug(f"Plotting {len(raw.annotations)} events...")
    
    events = []
    for ann in raw.annotations:
        events.append({
            "Time": ann["onset"],
            "Duration": ann["duration"],
            "Description": ann["description"]
        })
    
    fig = go.Figure()
    for i, event in enumerate(events):
        fig.add_trace(go.Scatter(
            x=[event["Time"], event["Time"] + event["Duration"]],
            y=[i, i],
            mode="lines+markers",
            line=dict(width=8),
            name=event["Description"],
            hovertemplate=f"Event: {event['Description']}<br>Start: {event['Time']:.2f}s<br>Duration: {event['Duration']:.2f}s"
        ))
    
    fig.update_layout(
        title="Event Timeline",
        xaxis_title="Time (s)",
        yaxis_title="Events",
        template="plotly_dark",
        height=300,
        showlegend=True
    )
    
    return fig

# ---------------- PDF REPORT ----------------
class PDF(FPDF):
    def header(self):
        if LOGO_B64:
            try:
                # Handle the data URL format
                img_data = LOGO_B64.split(',')[1] if ',' in LOGO_B64 else LOGO_B64
                img_bytes = base64.b64decode(img_data)
                temp_path = os.path.join(tempfile.gettempdir(), "temp_logo.png")
                with open(temp_path, "wb") as f:
                    f.write(img_bytes)
                self.image(temp_path, 10, 8, 33)
                os.remove(temp_path)
            except Exception as e:
                logger.debug(f"Logo insertion failed: {e}")
        
        self.set_font("Helvetica", "B", 14)
        self.cell(0, 10, "EEG Quality Report", 0, 1, "C")
        self.set_font("Helvetica", "I", 10)
        self.cell(0, 8, f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 0, 1, "C")
        
    def footer(self):
        self.set_y(-15)
        self.set_font("Helvetica", "I", 8)
        self.cell(0, 10, f"Page {self.page_no()}", 0, 0, "C")
        
    def add_plot(self, title, img_data):
        self.add_page()
        self.set_font("Helvetica", "B", 12)
        self.cell(0, 10, title, ln=True)
        if img_data:
            try:
                self.image(img_data, x=10, y=30, w=190)
            except Exception as e:
                logger.error(f"Failed to add plot {title}: {e}")

@monitor_performance
def export_report(raw, metrics, dest):
    """Generate PDF report with metrics and plots"""
    logger.info(f"Generating PDF report: {dest}")
    
    pdf = PDF()
    pdf.add_page()
    pdf.set_font("Helvetica", "", 11)
    
    # Add metrics
    pdf.cell(0, 10, "Quality Metrics", ln=True, align="C")
    pdf.ln(5)
    for k, v in metrics.items():
        pdf.cell(0, 8, f"{k}: {v}", ln=True)
    
    # Add plots
    plots = [
        ("Raw EEG Trace", plot_raw(raw, raw.ch_names[:10])),
        ("Power Spectral Density", plot_psd(raw)),
        ("Electrode Topography", plot_topo(raw)),
        ("Band Power", plot_bandpower(raw))
    ]
    
    for title, fig in plots:
        if fig:
            logger.debug(f"Adding plot: {title}")
            # Save plot to temp file
            img_path = os.path.join(tempfile.gettempdir(), f"{title.replace(' ', '_')}.png")
            try:
                if isinstance(fig, str):  # Base64 image
                    with open(img_path, "wb") as f:
                        f.write(base64.b64decode(fig.split(",")[1]))
                else:  # Plotly figure
                    fig.write_image(img_path, format="png")
                
                pdf.add_plot(title, img_path)
                os.remove(img_path)  # Clean up
            except Exception as e:
                logger.error(f"Failed to process plot {title}: {e}")
    
    # Add QR code
    pdf.add_page()
    pdf.set_font("Helvetica", "B", 12)
    pdf.cell(0, 10, "Scan to open data", ln=True, align="C")
    qr_img = qr_code("file://" + dest)
    if qr_img:
        try:
            qr_path = os.path.join(tempfile.gettempdir(), "qr_temp.png")
            with open(qr_path, "wb") as f:
                f.write(base64.b64decode(qr_img.split(",")[1]))
            pdf.image(qr_path, x=80, y=50, w=50)
            os.remove(qr_path)
        except Exception as e:
            logger.error(f"QR code insertion failed: {e}")
    
    pdf.output(dest)
    logger.info(f"PDF report saved: {dest}")
    return dest

# ---------------- TAIPY STATE ----------------
class EEGState(State):
    file: str = ""
    raw: mne.io.Raw = None
    ica: mne.preprocessing.ICA = None
    ref: str = "average"
    band: list = [1.0, 40.0]
    strict: str = "medium"
    picks: list = []
    metrics: dict = {}
    loading: bool = False
    progress: int = 0
    debug: bool = False
    theme: str = "dark"
    
    # Debug-related state
    debug_logs: list = []
    debug_level: str = "INFO"
    show_debug_panel: bool = False
    memory_usage: str = ""
    performance_stats: dict = {}
    
    # plot holders
    plot_raw = {}
    plot_psd = {}
    plot_topo = ""
    plot_bp = {}
    plot_ica = ""
    plot_events = {}
    event_df = []
    status: str = "Ready"

# Debug utility functions
def get_memory_usage():
    """Get current memory usage"""
    try:
        import psutil
        process = psutil.Process(os.getpid())
        return f"{process.memory_info().rss / 1024 / 1024:.1f} MB"
    except ImportError:
        return "N/A (psutil not installed)"

def update_debug_info(state: EEGState):
    """Update debug information in state"""
    if state.debug:
        state.debug_logs = debug_handler.get_logs()[-50:]  # Last 50 logs
        state.memory_usage = get_memory_usage()
        
        # Performance stats
        if state.raw:
            state.performance_stats = {
                "Channels": len(state.raw.ch_names),
                "Samples": state.raw.n_times,
                "Duration": f"{state.raw.times[-1]:.2f}s",
                "Memory": state.memory_usage,
                "Sampling Rate": f"{state.raw.info['sfreq']} Hz"
            }

# ---------------- ACTIONS ----------------
def load(state: EEGState):
    """Load EEG file and initialize plots"""
    if not state.file:
        notify(state, "warning", "Please select a file")
        return
    
    try:
        state.loading = True
        state.status = "Loading file..."
        notify(state, "info", "Loading file…")
        logger.info(f"Loading file: {state.file}")
        
        raw = load_raw(state.file)
        state.raw = raw
        state.picks = raw.ch_names
        state.status = "File loaded"
        
        plot_all(state)
        update_debug_info(state)
        notify(state, "success", "File loaded successfully!")
        
    except Exception as e:
        logger.error(f"Load failed: {e}")
        logger.debug(f"Load traceback: {traceback.format_exc()}")
        notify(state, "error", f"Load failed: {str(e)}")
        state.status = "Error loading file"
    finally:
        state.loading = False
        update_debug_info(state)

def delete(state: EEGState):
    """Reset all state and clear loaded data"""
    logger.info("Clearing all data")
    state.file = ""
    state.raw = None
    state.ica = None
    state.picks = []
    state.metrics = {}
    state.plot_raw = {}
    state.plot_psd = {}
    state.plot_topo = ""
    state.plot_bp = {}
    state.plot_ica = ""
    state.plot_events = {}
    state.event_df = []
    state.performance_stats = {}
    state.status = "Ready"
    update_debug_info(state)
    notify(state, "info", "Data cleared")

@monitor_performance
def preprocess_action(state: EEGState):
    """Apply preprocessing to EEG data"""
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        state.loading = True
        state.status = "Preprocessing..."
        notify(state, "info", "Preprocessing data…")
        logger.info(f"Starting preprocessing: ref={state.ref}, band={state.band}")
        
        state.raw = preprocess(state.raw, state.ref, *state.band)
        state.status = "Preprocessing complete"
        
        plot_all(state)
        update_debug_info(state)
        notify(state, "success", "Preprocessing complete")
        
    except Exception as e:
        logger.error(f"Preprocessing failed: {e}")
        logger.debug(f"Preprocessing traceback: {traceback.format_exc()}")
        notify(state, "error", f"Preprocessing failed: {str(e)}")
        state.status = "Error in preprocessing"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def ica_action(state: EEGState):
    """Run ICA decomposition"""
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        state.loading = True
        state.status = "Running ICA..."
        notify(state, "info", "Running ICA decomposition…")
        logger.info("Starting ICA decomposition")
        
        state.raw, state.ica = run_ica(state.raw)
        state.plot_ica = plot_ica(state.ica, state.raw)
        state.status = "ICA complete"
        
        plot_all(state)
        update_debug_info(state)
        notify(state, "success", "ICA decomposition complete")
        
    except Exception as e:
        logger.error(f"ICA failed: {e}")
        logger.debug(f"ICA traceback: {traceback.format_exc()}")
        notify(state, "error", f"ICA failed: {str(e)}")
        state.status = "Error in ICA"
    finally:
        state.loading = False
        update_debug_info(state)

@monitor_performance
def qa_action(state: EEGState):
    """Compute quality metrics"""
    if not state.raw:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        state.loading = True
        state.status = "Computing metrics..."
        notify(state, "info", "Computing quality metrics…")
        logger.info(f"Starting QA with strictness: {state.strict}")
        
        state.metrics = compute_metrics(state.raw, state.strict)
        state.status = "Metrics computed"
        
        update_debug_info(state)
        
        # Update status based on quality
        if state.metrics.get("Overall Quality") == "Good":
            notify(state, "success", f"Data quality is GOOD ({state.metrics.get('Quality Score', 'N/A')})")
        else:
            notify(state, "warning", f"Data quality is POOR ({state.metrics.get('Quality Score', 'N/A')})")
            
    except Exception as e:
        logger.error(f"QA failed: {e}")
        logger.debug(f"QA traceback: {traceback.format_exc()}")
        notify(state, "error", f"QA failed: {str(e)}")
        state.status = "Error in QA"
    finally:
        state.loading = False
        update_debug_info(state)