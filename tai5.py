from __future__ import annotations
# ------------------------- stdlib -------------------------
import os, json, math, tempfile, time, base64, io, logging, traceback, sys
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple, Union
import warnings
warnings.filterwarnings('ignore', category=RuntimeWarning)

# ------------------------- third-party -------------------------
import numpy as np
import pandas as pd
import scipy.signal as signal
import scipy.stats as stats
from scipy.integrate import simpson
import matplotlib
matplotlib.use("Agg")  # headless safe
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.figure_factory as ff

# EEG-specific imports
try:
    import mne
    from mne.preprocessing import ICA
    HAS_MNE = True
except ImportError:
    HAS_MNE = False
    print("Warning: MNE not available. Some features will be limited.")

# GUI imports
from taipy.gui import Gui, State, notify, Markdown

# PDF and QR
from fpdf import FPDF
import qrcode

# Try optional kaleido for Plotly static images
try:
    import kaleido  # noqa: F401
    HAS_KALEIDO = True
except ImportError:
    HAS_KALEIDO = False

# ---------------- CONFIGURATION ----------------
SAMPLE_RATE = 256  # Default sample rate for synthetic data
LOGO_SVG = """<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100">
    <defs>
        <linearGradient id="brainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
        </linearGradient>
    </defs>
    <circle cx="50" cy="50" r="45" stroke="#0ea5e9" stroke-width="3" fill="url(#brainGradient)" />
    <path d="M30 40 Q50 20 70 40 Q50 60 30 40" fill="#ffffff" opacity="0.3"/>
    <circle cx="40" cy="35" r="3" fill="#fbbf24"/>
    <circle cx="60" cy="35" r="3" fill="#fbbf24"/>
    <text x="50" y="75" font-family="Arial" font-size="12" font-weight="bold" 
          text-anchor="middle" fill="#ffffff">EEG</text>
</svg>"""

LOGO_B64 = "data:image/svg+xml;base64," + base64.b64encode(LOGO_SVG.encode()).decode()

# Color palettes for consistent theming
COLORS = {
    'primary': '#4f46e5',
    'secondary': '#06b6d4', 
    'success': '#10b981',
    'warning': '#f59e0b',
    'error': '#ef4444',
    'dark': '#1f2937',
    'light': '#f9fafb'
}

BAND_COLORS = {
    'Delta': '#ef4444',
    'Theta': '#f59e0b', 
    'Alpha': '#10b981',
    'Beta': '#3b82f6',
    'Gamma': '#8b5cf6'
}

# ---------------- LOGGING SETUP ----------------
class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for better debugging"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
    ]
)

# Apply colored formatter to console handler
for handler in logging.getLogger().handlers:
    if isinstance(handler, logging.StreamHandler):
        handler.setFormatter(ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        ))

logger = logging.getLogger("EEGVisualizer")

# ---------------- PERFORMANCE MONITORING ----------------
def monitor_performance(func):
    """Decorator to monitor function performance"""
    def wrapper(*args, **kwargs):
        func_name = getattr(func, '__name__', str(func))
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            elapsed = time.time() - start_time
            logger.debug(f"{func_name} completed in {elapsed:.3f}s")
            return result
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"{func_name} failed after {elapsed:.3f}s: {str(e)}")
            raise
    return wrapper

# ---------------- DATA GENERATION AND LOADING ----------------
@monitor_performance
def generate_synthetic_eeg(duration=60, sample_rate=256, n_channels=19, add_artifacts=True):
    """Generate realistic synthetic EEG data for demonstration"""
    logger.info(f"Generating synthetic EEG: {duration}s, {sample_rate}Hz, {n_channels} channels")
    
    n_samples = int(duration * sample_rate)
    times = np.linspace(0, duration, n_samples)
    
    # Standard 10-20 electrode names
    channel_names = [
        'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
        'F7', 'F8', 'T3', 'T4', 'T5', 'T6', 'Fz', 'Cz', 'Pz'
    ][:n_channels]
    
    data = np.zeros((n_channels, n_samples))
    
    for i in range(n_channels):
        # Base EEG components with realistic frequencies and amplitudes
        
        # Alpha rhythm (8-13 Hz) - more prominent in posterior regions
        alpha_amp = 20e-6 if 'O' in channel_names[i] or 'P' in channel_names[i] else 10e-6
        alpha_freq = 9 + np.random.normal(0, 1)
        alpha = alpha_amp * np.sin(2 * np.pi * alpha_freq * times)
        
        # Beta rhythm (13-30 Hz) - more in frontal/central
        beta_amp = 8e-6 if any(x in channel_names[i] for x in ['F', 'C']) else 5e-6
        beta_freq = 20 + np.random.normal(0, 3)
        beta = beta_amp * np.sin(2 * np.pi * beta_freq * times)
        
        # Theta rhythm (4-8 Hz)
        theta_amp = 12e-6
        theta_freq = 6 + np.random.normal(0, 0.5)
        theta = theta_amp * np.sin(2 * np.pi * theta_freq * times)
        
        # Delta rhythm (0.5-4 Hz)
        delta_amp = 30e-6
        delta_freq = 2 + np.random.normal(0, 0.3)
        delta = delta_amp * np.sin(2 * np.pi * delta_freq * times)
        
        # Gamma rhythm (30-100 Hz) - low amplitude
        gamma_amp = 3e-6
        gamma_freq = 40 + np.random.normal(0, 5)
        gamma = gamma_amp * np.sin(2 * np.pi * gamma_freq * times)
        
        # Combine components
        signal_clean = alpha + beta + theta + delta + gamma
        
        # Add pink noise (1/f characteristic of EEG)
        noise_amp = 5e-6
        white_noise = np.random.normal(0, 1, n_samples)
        # Simple pink noise approximation
        pink_noise = signal.lfilter([1], [1, -0.9], white_noise) * noise_amp
        
        data[i] = signal_clean + pink_noise
        
        if add_artifacts:
            # Add occasional artifacts
            
            # Eye blinks (more prominent in frontal channels)
            if 'Fp' in channel_names[i]:
                blink_times = np.random.poisson(0.3, int(duration))  # ~0.3 blinks per second
                for _ in range(sum(blink_times)):
                    blink_start = np.random.randint(0, n_samples - int(0.3 * sample_rate))
                    blink_duration = int(0.3 * sample_rate)  # 300ms blink
                    blink_signal = 100e-6 * signal.gaussian(blink_duration, blink_duration/6)
                    end_idx = min(blink_start + blink_duration, n_samples)
                    data[i, blink_start:end_idx] += blink_signal[:end_idx-blink_start]
            
            # Muscle artifacts (random high frequency bursts)
            if np.random.random() < 0.1:  # 10% chance per channel
                artifact_start = np.random.randint(0, n_samples - int(2 * sample_rate))
                artifact_duration = int(np.random.uniform(0.5, 2) * sample_rate)
                muscle_freq = np.random.uniform(50, 100)
                muscle_signal = 30e-6 * np.sin(2 * np.pi * muscle_freq * 
                                              times[artifact_start:artifact_start+artifact_duration])
                end_idx = min(artifact_start + artifact_duration, n_samples)
                data[i, artifact_start:end_idx] += muscle_signal[:end_idx-artifact_start]
    
    return data, times, channel_names, sample_rate

@monitor_performance
def load_eeg_file(filepath: str):
    """Load EEG file - supports various formats or generates synthetic data"""
    if not HAS_MNE:
        logger.warning("MNE not available, generating synthetic data")
        return generate_synthetic_eeg()
    
    try:
        ext = os.path.splitext(filepath)[1].lower()
        
        if ext == '.edf':
            raw = mne.io.read_raw_edf(filepath, preload=True, verbose=False)
        elif ext == '.bdf':
            raw = mne.io.read_raw_bdf(filepath, preload=True, verbose=False)
        elif ext == '.fif':
            raw = mne.io.read_raw_fif(filepath, preload=True, verbose=False)
        elif ext == '.set':
            raw = mne.io.read_raw_eeglab(filepath, preload=True, verbose=False)
        else:
            raise ValueError(f"Unsupported file format: {ext}")
        
        # Extract data
        data = raw.get_data()  # Shape: (n_channels, n_samples)
        times = raw.times
        channel_names = raw.ch_names
        sample_rate = raw.info['sfreq']
        
        logger.info(f"Loaded EEG: {len(channel_names)} channels, {len(times)} samples, "
                   f"{sample_rate} Hz, {times[-1]:.1f}s duration")
        
        return data, times, channel_names, sample_rate
        
    except Exception as e:
        logger.error(f"Failed to load {filepath}: {e}")
        logger.info("Generating synthetic data as fallback")
        return generate_synthetic_eeg()

# ---------------- SIGNAL PROCESSING ----------------
@monitor_performance
def apply_bandpass_filter(data: np.ndarray, sample_rate: float, 
                         low_freq: float, high_freq: float, method='butterworth'):
    """Apply bandpass filter to EEG data"""
    nyquist = sample_rate / 2
    
    # Validate frequencies
    if low_freq >= nyquist or high_freq >= nyquist:
        raise ValueError(f"Filter frequencies must be < Nyquist frequency ({nyquist} Hz)")
    
    if method == 'butterworth':
        # 4th order Butterworth filter
        sos = signal.butter(4, [low_freq, high_freq], btype='band', 
                          fs=sample_rate, output='sos')
        filtered_data = signal.sosfiltfilt(sos, data, axis=1)
    else:
        raise ValueError(f"Unsupported filter method: {method}")
    
    logger.info(f"Applied {method} bandpass filter: {low_freq}-{high_freq} Hz")
    return filtered_data

@monitor_performance
def apply_notch_filter(data: np.ndarray, sample_rate: float, 
                      notch_freq: float = 50.0, quality: float = 30.0):
    """Apply notch filter to remove line noise"""
    # Design notch filter
    b, a = signal.iirnotch(notch_freq, quality, sample_rate)
    filtered_data = signal.filtfilt(b, a, data, axis=1)
    
    logger.info(f"Applied notch filter at {notch_freq} Hz (Q={quality})")
    return filtered_data

@monitor_performance
def compute_power_spectral_density(data: np.ndarray, sample_rate: float, 
                                  method='welch', nperseg=None):
    """Compute power spectral density"""
    if nperseg is None:
        nperseg = min(2048, data.shape[1] // 8)
    
    if method == 'welch':
        freqs, psd = signal.welch(data, fs=sample_rate, nperseg=nperseg, axis=1)
    else:
        raise ValueError(f"Unsupported PSD method: {method}")
    
    return freqs, psd

@monitor_performance
def compute_band_power(psd: np.ndarray, freqs: np.ndarray, 
                      freq_bands: Dict[str, Tuple[float, float]]):
    """Compute power in specific frequency bands"""
    band_powers = {}
    
    for band_name, (low_freq, high_freq) in freq_bands.items():
        # Find frequency indices
        freq_mask = (freqs >= low_freq) & (freqs <= high_freq)
        if not np.any(freq_mask):
            logger.warning(f"No frequencies found for {band_name} band ({low_freq}-{high_freq} Hz)")
            band_powers[band_name] = np.zeros(psd.shape[0])
            continue
        
        # Integrate power using Simpson's rule
        band_power = simpson(psd[:, freq_mask], x=freqs[freq_mask], axis=1)
        band_powers[band_name] = band_power
    
    return band_powers

# ---------------- QUALITY ASSESSMENT ----------------
@monitor_performance
def assess_signal_quality(data: np.ndarray, sample_rate: float, 
                         channel_names: List[str]):
    """Comprehensive signal quality assessment"""
    logger.info("Computing signal quality metrics")
    
    # Convert to microvolts for intuitive values
    data_uv = data * 1e6
    
    metrics = {
        'channel_names': channel_names,
        'sample_rate': sample_rate,
        'duration': data.shape[1] / sample_rate,
        'n_channels': len(channel_names),
        'n_samples': data.shape[1]
    }
    
    # Per-channel metrics
    channel_metrics = {}
    
    for i, ch_name in enumerate(channel_names):
        ch_data = data_uv[i]
        
        # Basic amplitude metrics
        amplitude_range = np.ptp(ch_data)  # peak-to-peak
        rms = np.sqrt(np.mean(ch_data**2))  # RMS amplitude
        std_dev = np.std(ch_data)
        
        # Signal-to-noise ratio estimation
        # Estimate noise as high-frequency component
        if sample_rate > 100:
            # High-pass filter at 50 Hz to isolate noise
            sos = signal.butter(4, 50, btype='highpass', fs=sample_rate, output='sos')
            noise = signal.sosfiltfilt(sos, ch_data)
            noise_power = np.mean(noise**2)
            signal_power = np.mean(ch_data**2)
            snr = 10 * np.log10(signal_power / (noise_power + 1e-12))
        else:
            snr = np.nan
        
        # Kurtosis (measure of spikiness/artifacts)
        kurt = stats.kurtosis(ch_data)
        
        # Zero-crossing rate
        zero_crossings = np.sum(np.diff(np.sign(ch_data)) != 0) / len(ch_data)
        
        # Frequency content analysis
        freqs, psd = signal.welch(ch_data, fs=sample_rate, nperseg=min(1024, len(ch_data)//4))
        
        # Dominant frequency
        dominant_freq = freqs[np.argmax(psd)]
        
        # Power in different bands
        freq_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8),
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, min(100, sample_rate/2-1))
        }
        
        band_powers = compute_band_power(psd.reshape(1, -1), freqs, freq_bands)
        total_power = sum(band_powers.values())[0]
        
        # Relative band powers
        relative_powers = {band: power[0]/total_power if total_power > 0 else 0 
                          for band, power in band_powers.items()}
        
        channel_metrics[ch_name] = {
            'amplitude_range_uv': amplitude_range,
            'rms_uv': rms,
            'std_uv': std_dev,
            'snr_db': snr,
            'kurtosis': kurt,
            'zero_crossing_rate': zero_crossings,
            'dominant_frequency': dominant_freq,
            'total_power': total_power,
            **{f'{band.lower()}_power': power[0] for band, power in band_powers.items()},
            **{f'{band.lower()}_relative': rel_power for band, rel_power in relative_powers.items()}
        }
        
        # Quality flags
        quality_flags = []
        if amplitude_range < 5:  # Very low amplitude
            quality_flags.append('low_amplitude')
        if amplitude_range > 200:  # Very high amplitude
            quality_flags.append('high_amplitude')
        if kurt > 10:  # High kurtosis indicates artifacts
            quality_flags.append('high_kurtosis')
        if snr < 10:  # Low SNR
            quality_flags.append('low_snr')
            
        channel_metrics[ch_name]['quality_flags'] = quality_flags
    
    metrics['channel_metrics'] = channel_metrics
    
    # Overall quality assessment
    n_good_channels = sum(1 for ch_metrics in channel_metrics.values() 
                         if len(ch_metrics['quality_flags']) == 0)
    overall_quality = n_good_channels / len(channel_names)
    
    if overall_quality > 0.8:
        quality_grade = 'Excellent'
    elif overall_quality > 0.6:
        quality_grade = 'Good'
    elif overall_quality > 0.4:
        quality_grade = 'Fair'
    else:
        quality_grade = 'Poor'
    
    metrics['overall_quality'] = {
        'grade': quality_grade,
        'score': overall_quality,
        'good_channels': n_good_channels,
        'total_channels': len(channel_names)
    }
    
    return metrics

# ---------------- ADVANCED ANALYSIS ----------------
@monitor_performance
def compute_connectivity_analysis(data: np.ndarray, method='coherence'):
    """Compute functional connectivity between channels"""
    n_channels = data.shape[0]
    connectivity_matrix = np.zeros((n_channels, n_channels))
    
    if method == 'correlation':
        # Pearson correlation
        connectivity_matrix = np.corrcoef(data)
    elif method == 'coherence':
        # Magnitude squared coherence (simplified)
        for i in range(n_channels):
            for j in range(i+1, n_channels):
                # Simplified coherence computation
                corr_coef = np.corrcoef(data[i], data[j])[0, 1]
                connectivity_matrix[i, j] = connectivity_matrix[j, i] = corr_coef**2
        np.fill_diagonal(connectivity_matrix, 1.0)
    
    return connectivity_matrix

@monitor_performance
def detect_artifacts(data: np.ndarray, sample_rate: float, channel_names: List[str]):
    """Detect various types of artifacts in EEG data"""
    artifacts = {
        'eye_blinks': [],
        'muscle_artifacts': [],
        'electrode_artifacts': [],
        'high_amplitude_events': []
    }
    
    data_uv = data * 1e6  # Convert to microvolts
    
    # Eye blink detection (high amplitude in frontal channels)
    frontal_channels = [i for i, name in enumerate(channel_names) 
                       if any(fp in name.upper() for fp in ['FP', 'AF'])]
    
    for ch_idx in frontal_channels:
        ch_data = data_uv[ch_idx]
        # Find peaks above threshold
        peaks, properties = signal.find_peaks(np.abs(ch_data), height=80, distance=int(0.5*sample_rate))
        for peak in peaks:
            artifacts['eye_blinks'].append({
                'channel': channel_names[ch_idx],
                'time': peak / sample_rate,
                'amplitude': ch_data[peak]
            })
    
    # Muscle artifact detection (high frequency, high amplitude)
    for i, ch_name in enumerate(channel_names):
        ch_data = data_uv[i]
        
        # High-pass filter to isolate high-frequency content
        if sample_rate > 100:
            sos = signal.butter(4, 30, btype='highpass', fs=sample_rate, output='sos')
            hf_data = signal.sosfiltfilt(sos, ch_data)
            
            # Find periods of high power in high frequencies
            window_size = int(1 * sample_rate)  # 1-second windows
            for start in range(0, len(hf_data) - window_size, window_size//2):
                window = hf_data[start:start + window_size]
                if np.std(window) > 20:  # Threshold for muscle artifact
                    artifacts['muscle_artifacts'].append({
                        'channel': ch_name,
                        'start_time': start / sample_rate,
                        'duration': window_size / sample_rate,
                        'power': np.std(window)
                    })
    
    # High amplitude event detection
    for i, ch_name in enumerate(channel_names):
        ch_data = data_uv[i]
        threshold = 5 * np.std(ch_data)  # 5 sigma events
        high_amp_samples = np.where(np.abs(ch_data) > threshold)[0]
        
        if len(high_amp_samples) > 0:
            # Group consecutive samples
            groups = []
            current_group = [high_amp_samples[0]]
            
            for sample in high_amp_samples[1:]:
                if sample - current_group[-1] <= sample_rate * 0.1:  # Within 100ms
                    current_group.append(sample)
                else:
                    groups.append(current_group)
                    current_group = [sample]
            groups.append(current_group)
            
            for group in groups:
                artifacts['high_amplitude_events'].append({
                    'channel': ch_name,
                    'start_time': group[0] / sample_rate,
                    'duration': (group[-1] - group[0]) / sample_rate,
                    'peak_amplitude': np.max(np.abs(ch_data[group]))
                })
    
    return artifacts

# ---------------- VISUALIZATION FUNCTIONS ----------------
@monitor_performance
def create_time_series_plot(data: np.ndarray, times: np.ndarray, 
                           channel_names: List[str], selected_channels: List[str] = None,
                           time_window: Tuple[float, float] = None):
    """Create interactive time series plot of EEG data"""
    
    # Select channels
    if selected_channels:
        ch_indices = [i for i, name in enumerate(channel_names) if name in selected_channels]
        plot_data = data[ch_indices]
        plot_names = selected_channels
    else:
        # Show first 10 channels if none selected
        n_show = min(10, len(channel_names))
        plot_data = data[:n_show]
        plot_names = channel_names[:n_show]
    
    # Apply time window
    if time_window:
        start_idx = np.searchsorted(times, time_window[0])
        end_idx = np.searchsorted(times, time_window[1])
        plot_times = times[start_idx:end_idx]
        plot_data = plot_data[:, start_idx:end_idx]
    else:
        plot_times = times
    
    # Convert to microvolts
    plot_data_uv = plot_data * 1e6
    
    # Create subplots
    fig = make_subplots(
        rows=len(plot_names), cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,
        subplot_titles=[f"<b>{name}</b>" for name in plot_names]
    )
    
    # Color palette
    colors = px.colors.qualitative.Set3
    
    for i, (ch_name, ch_data) in enumerate(zip(plot_names, plot_data_uv)):
        fig.add_trace(
            go.Scatter(
                x=plot_times,
                y=ch_data,
                mode='lines',
                name=ch_name,
                line=dict(width=1.5, color=colors[i % len(colors)]),
                hovertemplate=f"<b>{ch_name}</b><br>" +
                             "Time: %{x:.2f}s<br>" +
                             "Amplitude: %{y:.1f} µV<extra></extra>",
                showlegend=False
            ),
            row=i+1, col=1
        )
        
        # Update y-axis
        fig.update_yaxes(
            title_text="µV",
            row=i+1, col=1,
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(255,255,255,0.1)'
        )
    
    # Update layout
    fig.update_layout(
        title=dict(
            text=f"<b>EEG Time Series</b> - {len(plot_names)} channels",
            x=0.5,
            font=dict(size=18)
        ),
        height=max(400, 60 * len(plot_names) + 100),
        template="plotly_dark",
        hovermode="x unified",
        showlegend=False,
        margin=dict(l=60, r=20, t=60, b=50)
    )
    
    # Update x-axis only for the bottom subplot
    fig.update_xaxes(
        title_text="<b>Time (s)</b>",
        row=len(plot_names), col=1,
        showgrid=True,
        gridwidth=1,
        gridcolor='rgba(255,255,255,0.1)'
    )
    
    return fig

@monitor_performance
def create_psd_plot(data: np.ndarray, sample_rate: float, channel_names: List[str],
                   selected_channels: List[str] = None):
    """Create power spectral density plot"""
    
    # Select channels
    if selected_channels:
        ch_indices = [i for i, name in enumerate(channel_names) if name in selected_channels]
        plot_data = data[ch_indices]
        plot_names = selected_channels
    else:
        # Show first 15 channels if none selected
        n_show = min(15, len(channel_names))
        plot_data = data[:n_show]
        plot_names = channel_names[:n_show]
    
    # Compute PSD
    freqs, psd = compute_power_spectral_density(plot_data, sample_rate)
    
    # Convert to microvolts squared
    psd_uv2 = psd * (1e6)**2
    
    # Create plot
    fig = go.Figure()
    
    colors = px.colors.qualitative.Set2
    
    # Plot individual channels
    for i, (ch_name, ch_psd) in enumerate(zip(plot_names, psd_uv2)):
        fig.add_trace(
            go.Scatter(
                x=freqs,
                y=ch_psd,
                mode='lines',
                name=ch_name,
                line=dict(width=1.5, color=colors[i % len(colors)]),
                opacity=0.7,
                hovertemplate=f"<b>{ch_name}</b><br>" +
                             "Frequency: %{x:.1f} Hz<br>" +
                             "Power: %{y:.2e} µV²/Hz<extra></extra>",
            )
        )
    
    # Add median line
    median_psd = np.median(psd_uv2, axis=0)
    fig.add_trace(
        go.Scatter(
            x=freqs,
            y=median_psd,
            mode='lines',
            name='<b>Median</b>',
            line=dict(width=3, color='white', dash='dash'),
            hovertemplate="<b>Median</b><br>" +
                         "Frequency: %{x:.1f} Hz<br>" +
                         "Power: %{y:.2e} µV²/Hz<extra></extra>",
        )
    )
    
    # Add frequency band markers
    band_ranges = {
        'Delta': (0.5, 4, BAND_COLORS['Delta']),
        'Theta': (4, 8, BAND_COLORS['Theta']),
        'Alpha': (8, 13, BAND_COLORS['Alpha']),
        'Beta': (13, 30, BAND_COLORS['Beta']),
        'Gamma': (30, min(100, sample_rate/2), BAND_COLORS['Gamma'])
    }
    
    for band_name, (low, high, color) in band_ranges.items():
        if high <= freqs[-1]:
            fig.add_vrect(
                x0=low, x1=high,
                fillcolor=color,
                opacity=0.1,
                layer="below",
                line_width=0,
                annotation_text=band_name,
                annotation_position="top",
                annotation_font_size=10,
                annotation_font_color=color
            )
    
    # Update layout
    fig.update_layout(
        title=dict(
            text="<b>Power Spectral Density</b> (Welch Method)",
            x=0.5,
            font=dict(size=18)
        ),
        xaxis=dict(
            title="<b>Frequency (Hz)</b>",
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(255,255,255,0.1)'
        ),
        yaxis=dict(
            title="<b>Power (µV²/Hz)</b>",
            type="log",
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(255,255,255,0.1)'
        ),
        template="plotly_dark",
        height=500,
        legend=dict(
            orientation="v",
            yanchor="top",
            y=1,
            xanchor="left",
            x=1.02
        ),
        margin=dict(r=120)
    )
    
    return fig

@monitor_performance
def create_band_power_plot(data: np.ndarray, sample_rate: float, channel_names: List[str]):
    """Create frequency band power analysis plot"""
    
    # Compute PSD for all channels
    freqs, psd = compute_power_spectral_density(data, sample_rate)
    
    # Define frequency bands
    freq_bands = {
        'Delta': (0.5, 4),
        'Theta': (4, 8),
        'Alpha': (8, 13),
        'Beta': (13, 30),
        'Gamma': (30, min(100, sample_rate/2-1))
    }
    
    # Compute band powers
    band_powers = compute_band_power(psd, freqs, freq_bands)
    
    # Create DataFrame for plotting
    plot_data = []
    for ch_idx, ch_name in enumerate(channel_names):
        for band_name, powers in band_powers.items():
            plot_data.append({
                'Channel': ch_name,
                'Band': band_name,
                'Power': powers[ch_idx],
                'Log_Power': np.log10(powers[ch_idx] + 1e-12)
            })
    
    df = pd.DataFrame(plot_data)
    
    # Create grouped bar chart
    fig = px.bar(
        df, 
        x='Channel', 
        y='Log_Power',
        color='Band',
        color_discrete_map=BAND_COLORS,
        title="<b>Frequency Band Power Analysis</b>",
        labels={
            'Log_Power': '<b>Log Power (µV²/Hz)</b>',
            'Channel': '<b>Channel</b>',
            'Band': '<b>Frequency Band</b>'
        },
        hover_data={'Power': ':.2e'}
    )
    
    fig.update_layout(
        template="plotly_dark",
        height=500,
        xaxis_tickangle=-45,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="center",
            x=0.5
        )
    )
    
    return fig

@monitor_performance
def create_topography_plot(data: np.ndarray, channel_names: List[str], title: str = "EEG Topography"):
    """Create topographical brain map"""
    # This is a simplified version - in practice, you'd need electrode positions
    # For now, we'll create a heatmap-style visualization
    
    # Compute average power for each channel
    channel_power = np.mean(data**2, axis=1) * (1e6)**2  # Convert to µV²
    
    # Create a simple grid layout for channels (simplified topography)
    n_channels = len(channel_names)
    grid_size = int(np.ceil(np.sqrt(n_channels)))
    
    # Create a heatmap
    fig = go.Figure(data=go.Heatmap(
        z=channel_power.reshape(1, -1),
        x=channel_names,
        colorscale='Viridis',
        colorbar=dict(title="<b>Power (µV²)</b>"),
        hoverongaps=False,
        hovertemplate="<b>%{x}</b><br>Power: %{z:.2e} µV²<extra></extra>"
    ))
    
    fig.update_layout(
        title=dict(text=f"<b>{title}</b>", x=0.5, font=dict(size=18)),
        template="plotly_dark",
        height=200,
        yaxis=dict(showticklabels=False),
        xaxis=dict(tickangle=45)
    )
    
    return fig

@monitor_performance
def create_connectivity_plot(connectivity_matrix: np.ndarray, channel_names: List[str]):
    """Create connectivity matrix heatmap"""
    
    fig = go.Figure(data=go.Heatmap(
        z=connectivity_matrix,
        x=channel_names,
        y=channel_names,
        colorscale='RdBu_r',
        zmid=0,
        colorbar=dict(title="<b>Connectivity</b>"),
        hovertemplate="<b>%{y} ↔ %{x}</b><br>Connectivity: %{z:.3f}<extra></extra>"
    ))
    
    fig.update_layout(
        title=dict(text="<b>Functional Connectivity Matrix</b>", x=0.5, font=dict(size=18)),
        template="plotly_dark",
        height=600,
        xaxis=dict(tickangle=45),
        yaxis=dict(tickangle=0)
    )
    
    return fig

@monitor_performance
def create_quality_dashboard(quality_metrics: Dict):
    """Create comprehensive quality assessment dashboard"""
    
    # Extract metrics
    channel_metrics = quality_metrics['channel_metrics']
    overall_quality = quality_metrics['overall_quality']
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            "<b>Signal Quality by Channel</b>",
            "<b>SNR Distribution</b>",
            "<b>Amplitude Range</b>",
            "<b>Frequency Content</b>"
        ],
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    channels = list(channel_metrics.keys())
    
    # Plot 1: Quality flags count
    quality_counts = [len(channel_metrics[ch]['quality_flags']) for ch in channels]
    colors = ['green' if count == 0 else 'orange' if count <= 2 else 'red' for count in quality_counts]
    
    fig.add_trace(
        go.Bar(
            x=channels,
            y=quality_counts,
            name="Quality Issues",
            marker_color=colors,
            hovertemplate="<b>%{x}</b><br>Issues: %{y}<extra></extra>"
        ),
        row=1, col=1
    )
    
    # Plot 2: SNR distribution
    snr_values = [channel_metrics[ch]['snr_db'] for ch in channels if not np.isnan(channel_metrics[ch]['snr_db'])]
    if snr_values:
        fig.add_trace(
            go.Histogram(
                x=snr_values,
                nbinsx=20,
                name="SNR Distribution",
                marker_color=COLORS['primary'],
                opacity=0.7
            ),
            row=1, col=2
        )
    
    # Plot 3: Amplitude ranges
    amp_ranges = [channel_metrics[ch]['amplitude_range_uv'] for ch in channels]
    fig.add_trace(
        go.Scatter(
            x=channels,
            y=amp_ranges,
            mode='markers+lines',
            name="Amplitude Range",
            marker=dict(size=8, color=COLORS['secondary']),
            line=dict(width=2),
            hovertemplate="<b>%{x}</b><br>Range: %{y:.1f} µV<extra></extra>"
        ),
        row=2, col=1
    )
    
    # Plot 4: Dominant frequencies
    dom_freqs = [channel_metrics[ch]['dominant_frequency'] for ch in channels]
    fig.add_trace(
        go.Scatter(
            x=channels,
            y=dom_freqs,
            mode='markers',
            name="Dominant Frequency",
            marker=dict(
                size=10,
                color=dom_freqs,
                colorscale='Viridis',
                showscale=True,
                colorbar=dict(title="<b>Freq (Hz)</b>", x=1.1)
            ),
            hovertemplate="<b>%{x}</b><br>Dom. Freq: %{y:.1f} Hz<extra></extra>"
        ),
        row=2, col=2
    )
    
    # Update layout
    fig.update_layout(
        title=dict(
            text=f"<b>Signal Quality Dashboard</b> - Overall: {overall_quality['grade']} "
                 f"({overall_quality['good_channels']}/{overall_quality['total_channels']} channels)",
            x=0.5,
            font=dict(size=18)
        ),
        template="plotly_dark",
        height=700,
        showlegend=False
    )
    
    # Update axes
    fig.update_xaxes(tickangle=45)
    fig.update_yaxes(title_text="<b>Quality Issues</b>", row=1, col=1)
    fig.update_yaxes(title_text="<b>Count</b>", row=1, col=2)
    fig.update_yaxes(title_text="<b>Amplitude (µV)</b>", row=2, col=1)
    fig.update_yaxes(title_text="<b>Frequency (Hz)</b>", row=2, col=2)
    
    return fig

@monitor_performance
def create_artifact_plot(artifacts: Dict, duration: float):
    """Create artifact detection visualization"""
    
    fig = go.Figure()
    
    # Color mapping for different artifact types
    artifact_colors = {
        'eye_blinks': COLORS['warning'],
        'muscle_artifacts': COLORS['error'],
        'electrode_artifacts': COLORS['secondary'],
        'high_amplitude_events': 'purple'
    }
    
    y_positions = {'eye_blinks': 3, 'muscle_artifacts': 2, 'electrode_artifacts': 1, 'high_amplitude_events': 0}
    
    for artifact_type, artifact_list in artifacts.items():
        if not artifact_list:
            continue
            
        y_pos = y_positions[artifact_type]
        color = artifact_colors[artifact_type]
        
        for artifact in artifact_list:
            if artifact_type == 'eye_blinks':
                # Point markers for eye blinks
                fig.add_trace(go.Scatter(
                    x=[artifact['time']],
                    y=[y_pos],
                    mode='markers',
                    marker=dict(size=10, color=color),
                    name=artifact_type.replace('_', ' ').title(),
                    legendgroup=artifact_type,
                    showlegend=artifact == artifact_list[0],  # Show legend only for first item
                    hovertemplate=f"<b>{artifact_type.replace('_', ' ').title()}</b><br>"
                                f"Channel: {artifact['channel']}<br>"
                                f"Time: {artifact['time']:.2f}s<br>"
                                f"Amplitude: {artifact['amplitude']:.1f}µV<extra></extra>"
                ))
            else:
                # Rectangle markers for duration-based artifacts
                start_time = artifact.get('start_time', artifact.get('time', 0))
                duration_art = artifact.get('duration', 0.1)
                
                fig.add_shape(
                    type="rect",
                    x0=start_time,
                    y0=y_pos - 0.2,
                    x1=start_time + duration_art,
                    y1=y_pos + 0.2,
                    fillcolor=color,
                    opacity=0.7,
                    line=dict(width=0),
                )
                
                # Add invisible trace for legend
                if artifact == artifact_list[0]:
                    fig.add_trace(go.Scatter(
                        x=[start_time],
                        y=[y_pos],
                        mode='markers',
                        marker=dict(size=0.1, color=color),
                        name=artifact_type.replace('_', ' ').title(),
                        showlegend=True,
                        hoverinfo='skip'
                    ))
    
    # Update layout
    fig.update_layout(
        title=dict(text="<b>Artifact Detection Timeline</b>", x=0.5, font=dict(size=18)),
        xaxis=dict(
            title="<b>Time (s)</b>",
            range=[0, duration],
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(255,255,255,0.1)'
        ),
        yaxis=dict(
            title="<b>Artifact Type</b>",
            tickvals=list(y_positions.values()),
            ticktext=[name.replace('_', ' ').title() for name in y_positions.keys()],
            showgrid=True,
            gridwidth=1,
            gridcolor='rgba(255,255,255,0.1)'
        ),
        template="plotly_dark",
        height=400,
        hovermode="closest"
    )
    
    return fig

# ---------------- PDF REPORT GENERATION ----------------
class EnhancedPDF(FPDF):
    """Enhanced PDF class for EEG reports"""
    
    def header(self):
        # Add logo
        try:
            # Create temporary logo file
            logo_bytes = base64.b64decode(LOGO_B64.split(',')[1])
            logo_path = os.path.join(tempfile.gettempdir(), "eeg_logo.png")
            with open(logo_path, 'wb') as f:
                f.write(logo_bytes)
            
            self.image(logo_path, 10, 8, 25)
            os.remove(logo_path)
        except Exception as e:
            logger.warning(f"Could not add logo to PDF: {e}")
        
        # Title
        self.set_font('Arial', 'B', 20)
        self.set_text_color(0, 100, 200)
        self.cell(0, 15, 'EEG Analysis Report', 0, 1, 'C')
        
        # Subtitle
        self.set_font('Arial', 'I', 12)
        self.set_text_color(128, 128, 128)
        self.cell(0, 8, f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1, 'C')
        self.ln(5)
    
    def footer(self):
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.set_text_color(128, 128, 128)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')
    
    def add_section_header(self, title: str):
        self.ln(10)
        self.set_font('Arial', 'B', 16)
        self.set_text_color(0, 100, 200)
        self.cell(0, 10, title, 0, 1)
        self.set_draw_color(0, 100, 200)
        self.line(10, self.get_y(), 200, self.get_y())
        self.ln(5)
    
    def add_metrics_table(self, metrics: Dict):
        """Add quality metrics table"""
        self.add_section_header("Quality Metrics Summary")
        
        # Overall quality
        overall = metrics['overall_quality']
        self.set_font('Arial', '', 12)
        self.set_text_color(0, 0, 0)
        
        quality_color = {
            'Excellent': (0, 150, 0),
            'Good': (100, 150, 0),
            'Fair': (200, 150, 0),
            'Poor': (200, 0, 0)
        }.get(overall['grade'], (0, 0, 0))
        
        self.cell(60, 8, 'Overall Quality:', 0, 0)
        self.set_text_color(*quality_color)
        self.cell(0, 8, f"{overall['grade']} ({overall['score']:.1%})", 0, 1)
        
        self.set_text_color(0, 0, 0)
        self.cell(60, 8, 'Recording Duration:', 0, 0)
        self.cell(0, 8, f"{metrics['duration']:.1f} seconds", 0, 1)
        
        self.cell(60, 8, 'Sampling Rate:', 0, 0)
        self.cell(0, 8, f"{metrics['sample_rate']:.1f} Hz", 0, 1)
        
        self.cell(60, 8, 'Number of Channels:', 0, 0)
        self.cell(0, 8, f"{metrics['n_channels']}", 0, 1)
        
        self.cell(60, 8, 'Good Channels:', 0, 0)
        self.cell(0, 8, f"{overall['good_channels']}/{overall['total_channels']}", 0, 1)

@monitor_performance
def generate_pdf_report(data: np.ndarray, times: np.ndarray, channel_names: List[str], 
                       sample_rate: float, quality_metrics: Dict, artifacts: Dict,
                       output_path: str = None):
    """Generate comprehensive PDF report"""
    
    if output_path is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = f"EEG_Report_{timestamp}.pdf"
    
    logger.info(f"Generating PDF report: {output_path}")
    
    pdf = EnhancedPDF()
    pdf.add_page()
    
    # Add metrics table
    pdf.add_metrics_table(quality_metrics)
    
    # Add channel details
    pdf.add_section_header("Channel Analysis")
    pdf.set_font('Arial', '', 10)
    
    # Table header
    pdf.set_fill_color(230, 230, 230)
    pdf.cell(30, 8, 'Channel', 1, 0, 'C', True)
    pdf.cell(25, 8, 'Range (µV)', 1, 0, 'C', True)
    pdf.cell(25, 8, 'RMS (µV)', 1, 0, 'C', True)
    pdf.cell(25, 8, 'SNR (dB)', 1, 0, 'C', True)
    pdf.cell(30, 8, 'Dom. Freq', 1, 0, 'C', True)
    pdf.cell(35, 8, 'Quality Issues', 1, 1, 'C', True)
    
    # Table rows
    channel_metrics = quality_metrics['channel_metrics']
    for ch_name in channel_names[:20]:  # Limit to first 20 channels for space
        if ch_name in channel_metrics:
            metrics = channel_metrics[ch_name]
            pdf.cell(30, 6, ch_name, 1, 0, 'C')
            pdf.cell(25, 6, f"{metrics['amplitude_range_uv']:.1f}", 1, 0, 'C')
            pdf.cell(25, 6, f"{metrics['rms_uv']:.1f}", 1, 0, 'C')
            
            snr_text = f"{metrics['snr_db']:.1f}" if not np.isnan(metrics['snr_db']) else "N/A"
            pdf.cell(25, 6, snr_text, 1, 0, 'C')
            
            pdf.cell(30, 6, f"{metrics['dominant_frequency']:.1f} Hz", 1, 0, 'C')
            
            issues = len(metrics['quality_flags'])
            issue_text = "None" if issues == 0 else str(issues)
            pdf.cell(35, 6, issue_text, 1, 1, 'C')
    
    # Add artifacts summary
    if any(artifacts.values()):
        pdf.add_section_header("Detected Artifacts")
        
        for artifact_type, artifact_list in artifacts.items():
            if artifact_list:
                pdf.set_font('Arial', 'B', 12)
                pdf.cell(0, 8, f"{artifact_type.replace('_', ' ').title()}: {len(artifact_list)} detected", 0, 1)
    
    # Save PDF
    try:
        pdf.output(output_path)
        logger.info(f"PDF report saved successfully: {output_path}")
        return output_path
    except Exception as e:
        logger.error(f"Failed to save PDF report: {e}")
        raise

# ---------------- MAIN APPLICATION STATE ----------------
class EEGAnalyzerState:
    """Application state management"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        # Data
        self.data = None
        self.times = None
        self.channel_names = []
        self.sample_rate = 256
        self.filtered_data = None

        # Processing parameters
        self.low_freq = 1.0
        self.high_freq = 40.0
        self.notch_freq = 50.0
        self.apply_notch = True

        # Analysis results
        self.quality_metrics = {}
        self.artifacts = {}
        self.connectivity_matrix = None

        # UI state
        self.selected_channels = []
        self.time_window_start = 0.0
        self.time_window_end = 10.0
        self.processing_status = "Ready"
        self.is_processing = False

        # Plots - Initialize with empty figures to avoid None errors
        import plotly.graph_objects as go
        empty_fig = go.Figure()
        empty_fig.update_layout(
            title="No data loaded",
            template="plotly_dark",
            height=300
        )

        self.plot_timeseries = empty_fig
        self.plot_psd = empty_fig
        self.plot_bandpower = empty_fig
        self.plot_topography = empty_fig
        self.plot_connectivity = None
        self.plot_quality = None
        self.plot_artifacts = None

# Initialize global state
app_state = EEGAnalyzerState()

# ---------------- UI ACTION FUNCTIONS ----------------
@monitor_performance
def load_demo_data(state, **kwargs):
    """Load demonstration data"""
    logger.info("load_demo_data called with state and kwargs")
    try:
        app_state.is_processing = True
        app_state.processing_status = "Loading demonstration data..."
        
        # Generate synthetic EEG data
        data, times, channel_names, sample_rate = generate_synthetic_eeg(
            duration=30, sample_rate=256, n_channels=19, add_artifacts=True
        )
        
        app_state.data = data
        app_state.times = times
        app_state.channel_names = channel_names
        app_state.sample_rate = sample_rate
        app_state.selected_channels = channel_names[:8]  # Select first 8 channels
        app_state.time_window_end = min(10.0, times[-1])
        
        # Update plots
        update_all_plots()
        
        app_state.processing_status = f"Loaded demo data: {len(channel_names)} channels, {times[-1]:.1f}s"
        notify(state, "success", "Demo data loaded successfully!")
        
    except Exception as e:
        logger.error(f"Failed to load demo data: {e}")
        app_state.processing_status = "Error loading demo data"
        notify(state, "error", f"Error: {str(e)}")
    finally:
        app_state.is_processing = False

@monitor_performance
def apply_filters(state, **kwargs):
    """Apply filtering to the data"""
    if app_state.data is None:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        app_state.is_processing = True
        app_state.processing_status = "Applying filters..."
        
        # Apply bandpass filter
        filtered_data = apply_bandpass_filter(
            app_state.data, app_state.sample_rate, 
            app_state.low_freq, app_state.high_freq
        )
        
        # Apply notch filter if requested
        if app_state.apply_notch:
            filtered_data = apply_notch_filter(
                filtered_data, app_state.sample_rate, app_state.notch_freq
            )
        
        app_state.filtered_data = filtered_data
        
        # Update plots with filtered data
        update_all_plots()
        
        filter_desc = f"{app_state.low_freq}-{app_state.high_freq} Hz"
        if app_state.apply_notch:
            filter_desc += f", notch {app_state.notch_freq} Hz"
        
        app_state.processing_status = f"Filters applied: {filter_desc}"
        notify(state, "success", "Filters applied successfully!")
        
    except Exception as e:
        logger.error(f"Failed to apply filters: {e}")
        app_state.processing_status = "Error applying filters"
        notify(state, "error", f"Error: {str(e)}")
    finally:
        app_state.is_processing = False

@monitor_performance
def run_quality_analysis(state, **kwargs):
    """Run comprehensive quality analysis"""
    if app_state.data is None:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        app_state.is_processing = True
        app_state.processing_status = "Running quality analysis..."
        
        # Use filtered data if available, otherwise raw data
        analysis_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
        
        # Run quality assessment
        app_state.quality_metrics = assess_signal_quality(
            analysis_data, app_state.sample_rate, app_state.channel_names
        )
        
        # Detect artifacts
        app_state.artifacts = detect_artifacts(
            analysis_data, app_state.sample_rate, app_state.channel_names
        )
        
        # Update quality plots
        app_state.plot_quality = create_quality_dashboard(app_state.quality_metrics)
        app_state.plot_artifacts = create_artifact_plot(app_state.artifacts, app_state.times[-1])
        
        grade = app_state.quality_metrics['overall_quality']['grade']
        app_state.processing_status = f"Quality analysis complete: {grade}"
        
        # Notify based on quality
        if grade in ['Excellent', 'Good']:
            notify(state, "success", f"Data quality: {grade}")
        elif grade == 'Fair':
            notify(state, "warning", f"Data quality: {grade}")
        else:
            notify(state, "error", f"Data quality: {grade}")
        
    except Exception as e:
        logger.error(f"Failed to run quality analysis: {e}")
        app_state.processing_status = "Error in quality analysis"
        notify(state, "error", f"Error: {str(e)}")
    finally:
        app_state.is_processing = False

@monitor_performance
def run_connectivity_analysis(state, **kwargs):
    """Run connectivity analysis"""
    if app_state.data is None:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        app_state.is_processing = True
        app_state.processing_status = "Computing connectivity..."
        
        # Use filtered data if available, otherwise raw data
        analysis_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
        
        # Compute connectivity matrix
        app_state.connectivity_matrix = compute_connectivity_analysis(analysis_data)
        
        # Create connectivity plot
        app_state.plot_connectivity = create_connectivity_plot(
            app_state.connectivity_matrix, app_state.channel_names
        )
        
        app_state.processing_status = "Connectivity analysis complete"
        notify(state, "success", "Connectivity analysis completed!")
        
    except Exception as e:
        logger.error(f"Failed to run connectivity analysis: {e}")
        app_state.processing_status = "Error in connectivity analysis"
        notify(state, "error", f"Error: {str(e)}")
    finally:
        app_state.is_processing = False

@monitor_performance
def generate_report(state, **kwargs):
    """Generate comprehensive PDF report"""
    if app_state.data is None:
        notify(state, "warning", "No data loaded")
        return
    
    try:
        app_state.is_processing = True
        app_state.processing_status = "Generating PDF report..."
        
        # Ensure we have quality metrics
        if not app_state.quality_metrics:
            logger.info("Running quality analysis for report")
            analysis_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
            app_state.quality_metrics = assess_signal_quality(
                analysis_data, app_state.sample_rate, app_state.channel_names
            )
            app_state.artifacts = detect_artifacts(
                analysis_data, app_state.sample_rate, app_state.channel_names
            )
        
        # Generate PDF report
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_path = os.path.join(tempfile.gettempdir(), f"EEG_Report_{timestamp}.pdf")
        
        generate_pdf_report(
            analysis_data,
            app_state.times,
            app_state.channel_names,
            app_state.sample_rate,
            app_state.quality_metrics,
            app_state.artifacts,
            output_path
        )
        
        app_state.processing_status = f"Report generated: {os.path.basename(output_path)}"
        notify(state, "success", f"PDF report saved: {output_path}")
        
    except Exception as e:
        logger.error(f"Failed to generate report: {e}")
        app_state.processing_status = "Error generating report"
        notify(state, "error", f"Error: {str(e)}")
    finally:
        app_state.is_processing = False

@monitor_performance
def update_time_window(state, **kwargs):
    """Update time window for visualization"""
    if app_state.data is None:
        return
    
    # Ensure valid time window
    max_time = app_state.times[-1]
    app_state.time_window_start = max(0, min(app_state.time_window_start, max_time - 1))
    app_state.time_window_end = min(max_time, max(app_state.time_window_start + 1, app_state.time_window_end))
    
    # Update time series plot
    update_timeseries_plot()

@monitor_performance
def update_channel_selection(state, **kwargs):
    """Update selected channels for visualization"""
    if app_state.data is None:
        return
    
    # Limit selection to prevent performance issues
    if len(app_state.selected_channels) > 20:
        app_state.selected_channels = app_state.selected_channels[:20]
        notify(state, "warning", "Limited selection to 20 channels for performance")
    
    # Update plots
    update_timeseries_plot()
    update_psd_plot()

@monitor_performance
def clear_all_data(state, **kwargs):
    """Clear all data and reset application"""
    app_state.reset()
    notify(state, "info", "All data cleared")

@monitor_performance
def update_all_plots():
    """Update all visualization plots"""
    if app_state.data is None:
        return
    
    # Use filtered data if available, otherwise raw data
    plot_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
    
    # Update plots
    update_timeseries_plot()
    update_psd_plot()
    update_bandpower_plot()
    update_topography_plot()

@monitor_performance
def update_timeseries_plot():
    """Update time series plot"""
    if app_state.data is None:
        return
    
    plot_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
    time_window = (app_state.time_window_start, app_state.time_window_end)
    
    app_state.plot_timeseries = create_time_series_plot(
        plot_data, app_state.times, app_state.channel_names,
        app_state.selected_channels, time_window
    )

@monitor_performance
def update_psd_plot():
    """Update PSD plot"""
    if app_state.data is None:
        return
    
    plot_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
    
    app_state.plot_psd = create_psd_plot(
        plot_data, app_state.sample_rate, app_state.channel_names,
        app_state.selected_channels
    )

@monitor_performance
def update_bandpower_plot():
    """Update band power plot"""
    if app_state.data is None:
        return
    
    plot_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
    
    app_state.plot_bandpower = create_band_power_plot(
        plot_data, app_state.sample_rate, app_state.channel_names
    )

@monitor_performance
def update_topography_plot():
    """Update topography plot"""
    if app_state.data is None:
        return
    
    plot_data = app_state.filtered_data if app_state.filtered_data is not None else app_state.data
    
    app_state.plot_topography = create_topography_plot(
        plot_data, app_state.channel_names, "Channel Power Distribution"
    )

# ---------------- GUI PAGE DEFINITION ----------------
page = """
<|toggle|theme|>

# 🧠 Advanced EEG Analyzer

<|layout|columns=1 1 1 1|gap=15px|class_name=control-panel|>
<|Load Demo Data|button|on_action=load_demo_data|active={not app_state.is_processing}|class_name=primary-button|>
<|Apply Filters|button|on_action=apply_filters|active={not app_state.is_processing and app_state.data is not None}|class_name=secondary-button|>
<|Quality Analysis|button|on_action=run_quality_analysis|active={not app_state.is_processing and app_state.data is not None}|class_name=success-button|>
<|Clear All|button|on_action=clear_all_data|active={not app_state.is_processing}|class_name=danger-button|>
<|>

## ⚙️ Processing Parameters

<|layout|columns=1 1 1 1|gap=15px|class_name=parameter-panel|>
<|{app_state.low_freq}|slider|min=0.1|max=20|step=0.1|label=Low Freq (Hz)|class_name=slider-control|>
<|{app_state.high_freq}|slider|min=10|max=100|step=1|label=High Freq (Hz)|class_name=slider-control|>
<|{app_state.notch_freq}|selector|lov=50;60|label=Notch Freq (Hz)|class_name=selector-control|>
<|{app_state.apply_notch}|toggle|label=Apply Notch Filter|class_name=toggle-control|>
<|>

<|layout|columns=2 1|gap=20px|>
<|{app_state.selected_channels}|selector|multiple=True|lov={app_state.channel_names}|label=Channels (max 20)|class_name=channel-selector|>
<|{app_state.processing_status}|text|class_name=status-display|>
<|>

## 📊 Time Window Control

<|layout|columns=1 1|gap=15px|class_name=time-control|>
<|{app_state.time_window_start}|slider|min=0|max={app_state.times[-1] if app_state.times is not None else 30}|step=0.1|label=Start Time (s)|on_action=update_time_window|>
<|{app_state.time_window_end}|slider|min=1|max={app_state.times[-1] if app_state.times is not None else 30}|step=0.1|label=End Time (s)|on_action=update_time_window|>
<|>

<|part|render={app_state.is_processing}|class_name=loading-indicator|>
## 🔄 Processing...
<|Loading...|text|class_name=loading-text|>
<|>

<|part|render={app_state.data is not None and not app_state.is_processing}|class_name=main-analysis|>

## 📈 Signal Visualization

<|layout|columns=1|class_name=visualization-section|>

### 🌊 EEG Time Series
<|part|class_name=plot-container|>
<|{app_state.plot_timeseries}|chart|class_name=main-chart|>
<|>

<|layout|columns=1 1|gap=20px|>
<|part|class_name=plot-container|>
### 📊 Power Spectral Density
<|{app_state.plot_psd}|chart|class_name=secondary-chart|>
<|>

<|part|class_name=plot-container|>
### 🎯 Frequency Bands
<|{app_state.plot_bandpower}|chart|class_name=secondary-chart|>
<|>
<|>

<|part|class_name=plot-container|>
### 🗺️ Topographic Distribution
<|{app_state.plot_topography}|chart|class_name=topography-chart|>
<|>

<|>

## 🔬 Advanced Analysis

<|layout|columns=1 1|gap=20px|>
<|Connectivity Analysis|button|on_action=run_connectivity_analysis|active={not app_state.is_processing and app_state.data is not None}|class_name=analysis-button|>
<|Generate Report|button|on_action=generate_report|active={not app_state.is_processing and app_state.data is not None}|class_name=report-button|>
<|>

<|part|render={app_state.plot_connectivity is not None}|class_name=connectivity-section|>
### 🔗 Functional Connectivity
<|part|class_name=plot-container|>
<|{app_state.plot_connectivity}|chart|class_name=connectivity-chart|>
<|>
<|>

<|part|render={app_state.plot_quality is not None}|class_name=quality-section|>
### 📋 Signal Quality Assessment
<|part|class_name=plot-container|>
<|{app_state.plot_quality}|chart|class_name=quality-chart|>
<|>
<|>

<|part|render={app_state.plot_artifacts is not None}|class_name=artifacts-section|>
### ⚠️ Artifact Detection
<|part|class_name=plot-container|>
<|{app_state.plot_artifacts}|chart|class_name=artifacts-chart|>
<|>
<|>

<|>
"""

# ---------------- ENHANCED CSS STYLING ----------------
css_style = """
/* Main theme colors */
:root {
    --primary-color: #4f46e5;
    --secondary-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --dark-bg: #1f2937;
    --card-bg: rgba(31, 41, 55, 0.8);
    --border-color: rgba(75, 85, 99, 0.5);
}

/* Global styles */
body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: #f8fafc;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Control panels */
.control-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.parameter-panel {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    backdrop-filter: blur(10px);
}

.time-control {
    background: rgba(79, 70, 229, 0.1);
    border: 1px solid rgba(79, 70, 229, 0.3);
    border-radius: 12px;
    padding: 15px;
    margin: 10px 0;
}

/* Button styles */
.primary-button {
    background: linear-gradient(135deg, var(--primary-color), #6366f1);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.6);
}

.secondary-button {
    background: linear-gradient(135deg, var(--secondary-color), #0891b2);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
}

.success-button {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.danger-button {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.analysis-button, .report-button {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 15px 30px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
}

/* Control elements */
.slider-control, .selector-control, .toggle-control {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 10px;
}

.channel-selector {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    max-height: 120px;
    overflow-y: auto;
}

.status-display {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 8px;
    padding: 15px;
    font-weight: 600;
    font-size: 16px;
    color: var(--success-color);
    text-align: center;
}

/* Loading indicator */
.loading-indicator {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    animation: pulse 2s ease-in-out infinite alternate;
}

.loading-text {
    color: var(--warning-color);
    font-size: 18px;
    font-weight: 600;
}

@keyframes pulse {
    from { opacity: 0.6; }
    to { opacity: 1.0; }
}

/* Visualization sections */
.main-analysis {
    margin-top: 30px;
}

.visualization-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.plot-container {
    background: rgba(17, 24, 39, 0.6);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    backdrop-filter: blur(5px);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.plot-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
}

.main-chart {
    min-height: 400px;
    border-radius: 8px;
}

.secondary-chart {
    min-height: 350px;
    border-radius: 8px;
}

.topography-chart, .connectivity-chart {
    min-height: 400px;
    border-radius: 8px;
}

.quality-chart, .artifacts-chart {
    min-height: 450px;
    border-radius: 8px;
}

/* Section headers */
.connectivity-section, .quality-section, .artifacts-section {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 25px;
    margin: 25px 0;
    backdrop-filter: blur(10px);
}

/* Section titles */
h1, h2, h3 {
    color: #f8fafc;
    margin-bottom: 15px;
}

h1 {
    font-size: 2.5em;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: 1.8em;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 8px;
}

h3 {
    font-size: 1.4em;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 12px;
}

/* Responsive design */
@media (max-width: 1200px) {
    .plot-container {
        margin: 10px 0;
        padding: 15px;
    }
    
    .control-panel, .parameter-panel {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2em;
    }
    
    .plot-container {
        padding: 10px;
        margin: 8px 0;
    }
    
    .main-chart, .secondary-chart {
        min-height: 300px;
    }
    
    .control-panel, .parameter-panel {
        padding: 10px;
        margin: 10px 0;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* Animations */
.control-panel, .parameter-panel, .plot-container {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus states */
.taipy-selector:focus,
.taipy-slider:focus,
.taipy-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Improved button disabled state */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Notification styles */
.taipy-notification {
    border-radius: 8px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.taipy-notification.success {
    background: rgba(16, 185, 129, 0.9);
    border: 1px solid var(--success-color);
}

.taipy-notification.error {
    background: rgba(239, 68, 68, 0.9);
    border: 1px solid var(--error-color);
}

.taipy-notification.warning {
    background: rgba(245, 158, 11, 0.9);
    border: 1px solid var(--warning-color);
}
"""

# ---------------- MAIN APPLICATION ----------------
if __name__ == "__main__":
    try:
        logger.info("🚀 Starting Enhanced EEG Visualizer")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"NumPy version: {np.__version__}")
        logger.info(f"Matplotlib version: {matplotlib.__version__}")
        
        if HAS_MNE:
            logger.info(f"MNE version: {mne.__version__}")
        else:
            logger.warning("MNE not available - using synthetic data only")
        
        # Configure matplotlib for better performance
        plt.rcParams['figure.facecolor'] = 'black'
        plt.rcParams['axes.facecolor'] = 'black'
        plt.rcParams['text.color'] = 'white'
        plt.rcParams['axes.labelcolor'] = 'white'
        plt.rcParams['xtick.color'] = 'white'
        plt.rcParams['ytick.color'] = 'white'
        
        # Create and configure GUI
        gui = Gui(page)

        # Add custom CSS
        gui._config.custom_css = css_style
        
        logger.info("🎨 GUI initialized with custom styling")
        logger.info("🖥️  Starting web server...")
        
        # Run the application
        gui.run(
            title="🧠 Advanced EEG Analyzer - Professional Edition",
            port=5000,
            host="127.0.0.1",
            dark_mode=True,
            debug=False,
            use_reloader=False,
            favicon=LOGO_B64
        )
        
    except KeyboardInterrupt:
        logger.info("👋 Application stopped by user")
    except Exception as e:
        logger.error(f"💥 Failed to start application: {e}")
        logger.debug(f"Full traceback:\n{traceback.format_exc()}")
        raise
    finally:
        logger.info("🏁 EEG Analyzer shutdown complete")