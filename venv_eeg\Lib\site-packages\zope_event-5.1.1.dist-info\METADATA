Metadata-Version: 2.4
Name: zope.event
Version: 5.1.1
Summary: Very basic event publishing system
Home-page: https://github.com/zopefoundation/zope.event
Author: Zope Foundation and Contributors
Author-email: <EMAIL>
License: ZPL-2.1
Keywords: event framework dispatch subscribe publish
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Zope Public License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: Jython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Framework :: Zope :: 3
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
License-File: LICENSE.txt
Requires-Dist: setuptools>=75.8.2
Provides-Extra: docs
Requires-Dist: Sphinx; extra == "docs"
Provides-Extra: test
Requires-Dist: zope.testrunner; extra == "test"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

==========
zope.event
==========

.. image:: https://img.shields.io/pypi/v/zope.event.svg
        :target: https://pypi.python.org/pypi/zope.event/
        :alt: Latest Version

.. image:: https://github.com/zopefoundation/zope.event/actions/workflows/tests.yml/badge.svg
        :target: https://github.com/zopefoundation/zope.event/actions/workflows/tests.yml

.. image:: https://readthedocs.org/projects/zopeevent/badge/?version=latest
        :target: http://zopeevent.readthedocs.org/en/latest/
        :alt: Documentation Status

The ``zope.event`` package provides a simple event system, including:

- An event publishing API, intended for use by applications which are
  unaware of any subscribers to their events.

- A very simple synchronous event-dispatching system, on which more sophisticated
  event dispatching systems can be built. For example, a type-based
  event dispatching system that builds on ``zope.event`` can be found in
  ``zope.component``.

Please see http://zopeevent.readthedocs.io/ for the documentation.

==========================
 ``zope.event`` Changelog
==========================

5.1.1 (2025-07-22)
==================

- Require ``setuptools >= 75.8.2`` to prevent problems with the new packaging
  standard.
  (`#30 <https://github.com/zopefoundation/zope.event/issues/30>`_)


5.1 (2025-06-26)
================

- Add support for Python 3.12 and 3.13.

- Drop support for Python 3.7 and 3.8.


5.0 (2023-06-23)
================

- Drop support for Python 2.7, 3.5, 3.6.


4.6 (2022-12-15)
================

- Port documentation to Python 3.

- Add support for Python 3.10, 3.11.


4.5.0 (2020-09-18)
==================

- Add support for Python 3.8 and 3.9.

- Remove support for Python 3.4.


4.4 (2018-10-05)
================

- Add support for Python 3.7


4.3.0 (2017-07-25)
==================

- Add support for Python 3.6.

- Drop support for Python 3.3.


4.2.0 (2016-02-17)
==================

- Add support for Python 3.5.

- Drop support for Python 2.6 and 3.2.


4.1.0 (2015-10-18)
==================

- Require 100% branch (as well as statement) coverage.

- Add a simple class-based handler implementation.


4.0.3 (2014-03-19)
==================

- Add support for Python 3.4.

- Update ``boostrap.py`` to version 2.2.


4.0.2 (2012-12-31)
==================

- Flesh out PyPI Trove classifiers.

- Add support for jython 2.7.


4.0.1 (2012-11-21)
==================

- Add support for Python 3.3.


4.0.0 (2012-05-16)
==================

- Automate build of Sphinx HTML docs and running doctest snippets via tox.

- Drop explicit support for Python 2.4 / 2.5 / 3.1.

- Add support for PyPy.


3.5.2 (2012-03-30)
==================

- This release is the last which will maintain support for Python 2.4 /
  Python 2.5.

- Add support for continuous integration using ``tox`` and ``jenkins``.

- Add 'setup.py dev' alias (runs ``setup.py develop`` plus installs
  ``nose`` and ``coverage``).

- Add 'setup.py docs' alias (installs ``Sphinx`` and dependencies).


3.5.1 (2011-08-04)
==================

- Add Sphinx documentation.


3.5.0 (2010-05-01)
==================

- Add change log to ``long-description``.

- Add support for Python 3.x.


3.4.1 (2009-03-03)
==================

- A few minor cleanups.


3.4.0 (2007-07-14)
==================

- Initial release as a separate project.
